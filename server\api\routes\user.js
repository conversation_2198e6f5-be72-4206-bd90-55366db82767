const express = require("express");

const router = express.Router();
const passport = require("passport");
const rateLimit = require("express-rate-limit");
const userController = require("../controllers/user");
const signupMiddleware = require("../middleware/signupMiddleware");
const { verifyToken } = require("../middleware/gen-token");
const checkBlacklistedToken = require("../middleware/checkBlacklistedToken");
const { 
  userLoginValidation, 
  passwordChangeValidation,
  twoFactorValidation,
  mongoIdValidation,
  userProfileValidation,
  storeValidation 
} = require("../middleware/inputValidation");

const deviceLimiter = require("../middleware/deviceLimiter");

const upload = require("../middleware/upload-photo");
// router.get('/users', userController.user_get_all)
const createAccountLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 1 hour window
  max: 5, // start blocking after 5 requests
  message:
    "Too many accounts created from this IP, please try again after an hour",
  standardHeaders: true, // Return rate limit info in headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Store signup attempts per device

router.post(
  "/signup",
  deviceLimiter,
  signupMiddleware,
  userController.userSignup
);

router.post("/login", deviceLimiter, userLoginValidation, userController.userLogin);

router.get('/check-code/:codeId', userController.checkCode);

router.post("/verify-code", deviceLimiter, twoFactorValidation, userController.verifyCode);

router.post("/verify-app-code", deviceLimiter, twoFactorValidation, userController.verifyAppCode);

router.post("/ifregiter", userController.checkIfregister);

router.post("/ifemailregiter", userController.checkIfEmailRegister);

// router.put('/forgot-pass', createAccountLimiter, userController.forgotPassword);

router.put("/app-forgot-pass", deviceLimiter, userController.AppForgotPassword);

router.put("/app-reset-pass/", deviceLimiter, userController.appResetPassword);

router.get(
  "/user/",
  passport.authenticate("user", { session: false }), checkBlacklistedToken,
  userController.userProfile
);

router.get(
  "/user/profile",
  passport.authenticate("user", { session: false }), checkBlacklistedToken,
  userController.userprivateProfile
);

router.post("/blacklist-token/", passport.authenticate("user", { session: false }), userController.CreateToken);

router.get(
  "/user/store",
  passport.authenticate("user", { session: false }),
  userController.userStoreAddress
);

router.get("/user/infomation/:id", mongoIdValidation, userController.getStoreInfomation);

router.put(
  "/user/store",
  passport.authenticate("user", { session: false }),
  storeValidation,
  userController.userStoreEdit
);

router.put(
  "/user/profile",
  passport.authenticate("user", { session: false }), checkBlacklistedToken,
  upload.single("imageFile"),
  userProfileValidation,
  userController.changeProfile
);

// router.put('/reset/:token', createAccountLimiter, verifyToken, userController.resetPassword);

router.put(
  "/change-pass",
  passport.authenticate("user", { session: false }), 
  checkBlacklistedToken,
  passwordChangeValidation,
  userController.changePassword
);

router.put(
  "/active-mail",
  passport.authenticate("user", { session: false }), checkBlacklistedToken,
  userController.activeMail
);

router.put("/active-authapp",  passport.authenticate("user", { session: false }), checkBlacklistedToken, userController.enableAuthApp);

router.put("/verify-authapp",  passport.authenticate("user", { session: false }), checkBlacklistedToken, userController.verifyAuthApp);

router.put(
  "/user/avatar",
  passport.authenticate("user", { session: false }), checkBlacklistedToken,
  upload.single("imageFile"),
  userController.changeAvatar
);

router.put(
  "/user/remove-avatar",
  passport.authenticate("user", { session: false }), checkBlacklistedToken,
  upload.single("imageFile"),
  userController.removeAvatar
);

router.put(
  "/change-pass",
  passport.authenticate("user", { session: false }), 
  checkBlacklistedToken,
  passwordChangeValidation,
  userController.changePassword
);

module.exports = router;
