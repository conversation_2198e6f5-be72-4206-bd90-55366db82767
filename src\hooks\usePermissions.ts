import { useAppContext } from "@/app/app-provider";

export const usePermissions = () => {
  const { user } = useAppContext();

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Admin always has all permissions
    if (user.rule === "admin") return true;
    
    // Check if user has specific permission
    return user.permissions?.includes(permission) || false;
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!user) return false;
    
    // Admin always has all permissions
    if (user.rule === "admin") return true;
    
    // Check if user has any of the specified permissions
    return permissions.some(permission => user.permissions?.includes(permission));
  };

  const getAllPermissions = (): string[] => {
    if (!user) return [];
    
    // Admin has all permissions
    if (user.rule === "admin") {
      return [
        // User Management
        "user_view",
        "user_add",
        "user_edit",
        "user_delete",
        "user_import_csv",

        // File Management
        "file_view",
        "file_upload",
        "file_delete",

        // System Settings
        "system_settings_view",
        "system_settings_edit",

        // Analytics
        "analytics_view",

        // Permission Management
        "permissions_manage"
      ];
    }
    
    return user.permissions || [];
  };

  return {
    hasPermission,
    hasAnyPermission,
    getAllPermissions,
    userPermissions: user?.permissions || [],
    isAdmin: user?.rule === "admin"
  };
};
