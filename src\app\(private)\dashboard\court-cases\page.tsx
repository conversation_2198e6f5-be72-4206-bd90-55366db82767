"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import courtCaseApiRequest from "@/apiRequests/courtCase";
import { 
  CourtCaseItemType, 
  CourtCaseSearchType,
  CourtCaseType,
  LoaiAnEnum,
  TrangThaiGiaiQuyetEnum,
  ThuTucApDungEnum
} from "@/schemaValidations/courtCase.schema";
import CourtCaseList from "@/components/CourtCase/CourtCaseList";
import CourtCaseForm from "@/components/CourtCase/CourtCaseForm";
import CourtCaseDetailModal from "@/components/CourtCase/CourtCaseDetailModal";
import CourtCaseImport from "@/components/CourtCase/CourtCaseImport";
import CourtCaseDetailedStats from "@/components/CourtCase/CourtCaseDetailedStats";
import PermissionGuard from "@/components/PermissionGuard";

const CourtCaseManagementPage = () => {
  const [cases, setCases] = useState<CourtCaseItemType[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCase, setSelectedCase] = useState<CourtCaseItemType | null>(null);
  const [editingCase, setEditingCase] = useState<CourtCaseItemType | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [showImport, setShowImport] = useState(false);
  const [showDetailedStats, setShowDetailedStats] = useState(false);
  const [stats, setStats] = useState<any>(null);

  // Search and filter states
  const [searchParams, setSearchParams] = useState<CourtCaseSearchType>({
    page: 1,
    limit: 20,
    search: '',
    loaiAn: undefined,
    trangThaiGiaiQuyet: undefined,
    thuTucApDung: undefined,
    fromDate: '',
    toDate: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });

  // Pagination state
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Fetch court cases
  const fetchCases = async () => {
    try {
      setLoading(true);
      const response = await courtCaseApiRequest.getCourtCases(searchParams);
      
      if (response.payload.success) {
        setCases(response.payload.cases);
        setPagination(response.payload.pagination);
      } else {
        toast.error('Không thể tải danh sách vụ việc');
      }
    } catch (error) {
      console.error('Error fetching court cases:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách vụ việc');
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStats = async () => {
    try {
      const response = await courtCaseApiRequest.getCourtCaseStats();
      if (response.payload.success) {
        setStats(response.payload.stats);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  useEffect(() => {
    fetchCases();
    fetchStats();
  }, [searchParams]);

  // Handle search
  const handleSearch = (newParams: Partial<CourtCaseSearchType>) => {
    setSearchParams(prev => ({
      ...prev,
      ...newParams,
      page: 1 // Reset to first page when searching
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({ ...prev, page }));
  };

  // Handle create case
  const handleCreateCase = async (data: CourtCaseType) => {
    try {
      setLoading(true);
      const response = await courtCaseApiRequest.createCourtCase(data);
      
      if (response.payload.success) {
        toast.success('Thêm vụ việc thành công');
        setShowForm(false);
        fetchCases();
        fetchStats();
      } else {
        toast.error('Không thể thêm vụ việc');
      }
    } catch (error: any) {
      console.error('Error creating court case:', error);
      toast.error(error.payload?.message || 'Có lỗi xảy ra khi thêm vụ việc');
    } finally {
      setLoading(false);
    }
  };

  // Handle update case
  const handleUpdateCase = async (data: CourtCaseType) => {
    if (!editingCase) return;

    try {
      setLoading(true);
      const response = await courtCaseApiRequest.updateCourtCase(editingCase._id, data);
      
      if (response.payload.success) {
        toast.success('Cập nhật vụ việc thành công');
        setEditingCase(null);
        fetchCases();
        fetchStats();
      } else {
        toast.error('Không thể cập nhật vụ việc');
      }
    } catch (error: any) {
      console.error('Error updating court case:', error);
      toast.error(error.payload?.message || 'Có lỗi xảy ra khi cập nhật vụ việc');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete case
  const handleDeleteCase = async (caseId: string) => {
    if (!confirm('Bạn có chắc chắn muốn xóa vụ việc này?')) return;

    try {
      const response = await courtCaseApiRequest.deleteCourtCase(caseId);
      
      if (response.payload.success) {
        toast.success('Xóa vụ việc thành công');
        fetchCases();
        fetchStats();
      } else {
        toast.error('Không thể xóa vụ việc');
      }
    } catch (error: any) {
      console.error('Error deleting court case:', error);
      toast.error(error.payload?.message || 'Có lỗi xảy ra khi xóa vụ việc');
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (caseIds: string[], action: "delete") => {
    if (action === "delete") {
      try {
        // Delete each case individually (since we don't have bulk delete endpoint yet)
        for (const caseId of caseIds) {
          await courtCaseApiRequest.deleteCourtCase(caseId);
        }
        toast.success(`Đã xóa ${caseIds.length} vụ việc`);
        fetchCases();
        fetchStats();
      } catch (error: any) {
        console.error('Error bulk deleting:', error);
        toast.error('Có lỗi xảy ra khi xóa hàng loạt');
      }
    }
  };

  // Handle export to Excel
  const handleExportExcel = async () => {
    try {
      toast.info('Đang xuất file Excel...');
      const response = await courtCaseApiRequest.exportCourtCases(searchParams);

      // Create blob and download
      const blob = new Blob([response.payload], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `danh-sach-vu-viec-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Xuất file Excel thành công');
    } catch (error: any) {
      console.error('Error exporting Excel:', error);
      toast.error('Có lỗi xảy ra khi xuất file Excel');
    }
  };

  return (
    <PermissionGuard requiredPermissions={['court_case_view']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Quản lý vụ việc tòa án</h1>
            <p className="text-gray-600 mt-1">
              Quản lý danh sách thụ lý và giải quyết vụ việc đề nghị giám đốc thẩm, tái thẩm
            </p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowDetailedStats(true)}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              📊 Thống kê chi tiết
            </button>
            <button
              onClick={() => setShowImport(true)}
              className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              📥 Import Excel
            </button>
            <button
              onClick={handleExportExcel}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              📊 Xuất Excel
            </button>
            <button
              onClick={() => setShowForm(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              ➕ Thêm vụ việc mới
            </button>
          </div>
        </div>

        {/* Statistics */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <div className="text-3xl mr-4">📊</div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Tổng số vụ việc</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
              </div>
            </div>
            
            {stats.byStatus.map((item: any) => {
              const icons: { [key: string]: string } = {
                'Chưa giải quyết': '🔴',
                'Đang giải quyết': '🟡',
                'Đã giải quyết': '🟢'
              };
              
              return (
                <div key={item._id} className="bg-white p-6 rounded-lg shadow">
                  <div className="flex items-center">
                    <div className="text-3xl mr-4">{icons[item._id] || '📋'}</div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">{item._id}</p>
                      <p className="text-2xl font-bold text-gray-900">{item.count}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Filters */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">Bộ lọc và tìm kiếm</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tìm kiếm
              </label>
              <input
                type="text"
                value={searchParams.search || ''}
                onChange={(e) => handleSearch({ search: e.target.value })}
                placeholder="Số thụ lý, bản án, tên..."
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Loại án
              </label>
              <select
                value={searchParams.loaiAn || ''}
                onChange={(e) => handleSearch({ loaiAn: e.target.value as any })}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Tất cả</option>
                {LoaiAnEnum.options.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trạng thái
              </label>
              <select
                value={searchParams.trangThaiGiaiQuyet || ''}
                onChange={(e) => handleSearch({ trangThaiGiaiQuyet: e.target.value as any })}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Tất cả</option>
                {TrangThaiGiaiQuyetEnum.options.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thủ tục áp dụng
              </label>
              <select
                value={searchParams.thuTucApDung || ''}
                onChange={(e) => handleSearch({ thuTucApDung: e.target.value as any })}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Tất cả</option>
                {ThuTucApDungEnum.options.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Từ ngày
              </label>
              <input
                type="date"
                value={searchParams.fromDate || ''}
                onChange={(e) => handleSearch({ fromDate: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Đến ngày
              </label>
              <input
                type="date"
                value={searchParams.toDate || ''}
                onChange={(e) => handleSearch({ toDate: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div className="flex items-end">
              <button
                onClick={() => setSearchParams({
                  page: 1,
                  limit: 20,
                  search: '',
                  loaiAn: undefined,
                  trangThaiGiaiQuyet: undefined,
                  thuTucApDung: undefined,
                  fromDate: '',
                  toDate: '',
                  sortBy: 'createdAt',
                  sortOrder: 'desc'
                })}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                Xóa bộ lọc
              </button>
            </div>
          </div>
        </div>

        {/* Court Case List */}
        <CourtCaseList
          cases={cases}
          onCaseSelect={setSelectedCase}
          onCaseEdit={setEditingCase}
          onCaseDelete={handleDeleteCase}
          onBulkAction={handleBulkAction}
          loading={loading}
        />

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="bg-white px-6 py-3 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Hiển thị {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} đến{' '}
                {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} trong tổng số{' '}
                {pagination.totalItems} vụ việc
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={!pagination.hasPrevPage}
                  className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Trước
                </button>
                
                <span className="px-3 py-2 text-sm text-gray-700">
                  Trang {pagination.currentPage} / {pagination.totalPages}
                </span>
                
                <button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Sau
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Modals */}
        {showForm && (
          <CourtCaseForm
            onSubmit={handleCreateCase}
            onCancel={() => setShowForm(false)}
            loading={loading}
          />
        )}

        {editingCase && (
          <CourtCaseForm
            courtCase={editingCase}
            onSubmit={handleUpdateCase}
            onCancel={() => setEditingCase(null)}
            loading={loading}
          />
        )}

        {selectedCase && (
          <CourtCaseDetailModal
            courtCase={selectedCase}
            onClose={() => setSelectedCase(null)}
            onEdit={setEditingCase}
            onDelete={handleDeleteCase}
          />
        )}

        {showImport && (
          <CourtCaseImport
            onImportComplete={() => {
              fetchCases();
              fetchStats();
            }}
            onClose={() => setShowImport(false)}
          />
        )}

        {showDetailedStats && (
          <CourtCaseDetailedStats
            onClose={() => setShowDetailedStats(false)}
          />
        )}
      </div>
    </PermissionGuard>
  );
};

export default CourtCaseManagementPage;
