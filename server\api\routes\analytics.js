const express = require("express");
const router = express.Router();
const passport = require("passport");
const checkBlacklistedToken = require("../middleware/checkBlacklistedToken");
const { verifyAdmin } = require("../middleware/is-admin");
const { dashboardLimiter } = require("../middleware/security");
const analyticsController = require("../controllers/analytics");

// Analytics routes - admin only
router.get(
  "/overview",
  dashboardLimiter,
  passport.authenticate('user', { session: false }),
  checkBlacklistedToken,
  verifyAdmin,
  analyticsController.getAnalyticsOverview
);

router.get(
  "/chart-data",
  dashboardLimiter,
  passport.authenticate('user', { session: false }),
  checkBlacklistedToken,
  verifyAdmin,
  analyticsController.getAnalyticsChartData
);

router.get(
  "/popular-posts",
  dashboardLimiter,
  passport.authenticate('user', { session: false }),
  checkBlacklistedToken,
  verifyAdmin,
  analyticsController.getPopularPosts
);

router.post(
  "/track-event",
  analyticsController.trackEvent
);

module.exports = router;
