import { z } from 'zod';

// Enums for validation - including empty string option
export const LoaiAnEnum = z.enum(['<PERSON><PERSON><PERSON> sự', '<PERSON><PERSON> sự', '<PERSON><PERSON><PERSON> chính', '<PERSON><PERSON> tế', 'Lao động', '<PERSON><PERSON><PERSON><PERSON>', '']);

export const HinhThucXuLyEnum = z.enum([
  'Tuyên vô tội',
  'Tuyên có tội - Tù',
  'Tuyên có tội - Phạt tiền',
  '<PERSON>yên có tội - <PERSON><PERSON>i tạo không giam giữ',
  '<PERSON><PERSON><PERSON><PERSON> có tội - Khác',
  '<PERSON><PERSON><PERSON> thường thiệt hại',
  '<PERSON>ừ chối khiếu kiện',
  'Ch<PERSON>p nhận khiếu kiện',
  'Khác',
  ''
]);

export const ThuTucApDungEnum = z.enum(['<PERSON><PERSON> thẩm', '<PERSON><PERSON><PERSON> thẩm', '<PERSON><PERSON><PERSON><PERSON> đốc thẩm', '<PERSON><PERSON><PERSON> thẩm', '']);

export const TrangThaiGiaiQuyetEnum = z.enum(['Chưa giải quyết', 'Đang giải quyết', 'Đã giải quyết']);

// Court Case Schema - All fields are optional for flexible data entry
export const CourtCaseSchema = z.object({
  // Thông tin thụ lý
  stt: z.number().int().positive().optional(), // Auto-generated
  loaiAn: LoaiAnEnum.optional(),
  soThuLy: z.string().max(100).optional(),
  ngayThuLy: z.string().optional(),
  tand: z.string().max(200).optional(),

  // Thông tin bản án/quyết định
  soBanAn: z.string().max(100).optional(),
  ngayBanHanh: z.string().optional(),
  biCaoNguoiKhieuKien: z.string().max(500).optional(),
  toiDanhNoiDung: z.string().max(1000).optional(),
  quanHePhatLuat: z.string().max(500).optional(),
  hinhThucXuLy: HinhThucXuLyEnum.optional(),
  thuTucApDung: ThuTucApDungEnum.optional(),
  thamPhanPhuTrach: z.string().max(200).optional(),
  truongPhoPhongKTNV: z.string().max(200).optional(),

  // Thông tin bổ sung
  ghiChu: z.string().max(1000).optional(),
  ghiChuKetQua: z.string().max(1000).optional(),
  trangThaiGiaiQuyet: TrangThaiGiaiQuyetEnum.default('Chưa giải quyết'),
});

// Update schema (allows partial updates)
export const CourtCaseUpdateSchema = CourtCaseSchema.partial();

// Search/Filter schema
export const CourtCaseSearchSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  search: z.string().optional(),
  loaiAn: LoaiAnEnum.optional(),
  trangThaiGiaiQuyet: TrangThaiGiaiQuyetEnum.optional(),
  thuTucApDung: ThuTucApDungEnum.optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
  sortBy: z.string().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Response types
export const CourtCaseItemSchema = CourtCaseSchema.extend({
  _id: z.string(),
  stt: z.number(),
  createdAt: z.string(),
  updatedAt: z.string(),
  createdBy: z.object({
    _id: z.string(),
    username: z.string(),
    email: z.string().optional(),
  }).optional(),
  updatedBy: z.object({
    _id: z.string(),
    username: z.string(),
    email: z.string().optional(),
  }).optional(),
  ngayThuLyFormatted: z.string().optional(),
  ngayBanHanhFormatted: z.string().optional(),
});

export const CourtCaseListResponseSchema = z.object({
  success: z.boolean(),
  cases: z.array(CourtCaseItemSchema),
  pagination: z.object({
    currentPage: z.number(),
    totalPages: z.number(),
    totalItems: z.number(),
    itemsPerPage: z.number(),
    hasNextPage: z.boolean(),
    hasPrevPage: z.boolean(),
  }),
});

export const CourtCaseStatsSchema = z.object({
  success: z.boolean(),
  stats: z.object({
    total: z.number(),
    byStatus: z.array(z.object({
      _id: z.string(),
      count: z.number(),
    })),
    byType: z.array(z.object({
      _id: z.string(),
      count: z.number(),
    })),
    byProcedure: z.array(z.object({
      _id: z.string(),
      count: z.number(),
    })),
  }),
});

// Type exports
export type CourtCaseType = z.infer<typeof CourtCaseSchema>;
export type CourtCaseUpdateType = z.infer<typeof CourtCaseUpdateSchema>;
export type CourtCaseItemType = z.infer<typeof CourtCaseItemSchema>;
export type CourtCaseSearchType = z.infer<typeof CourtCaseSearchSchema>;
export type CourtCaseListResponseType = z.infer<typeof CourtCaseListResponseSchema>;
export type CourtCaseStatsType = z.infer<typeof CourtCaseStatsSchema>;

// Enum type exports
export type LoaiAnType = z.infer<typeof LoaiAnEnum>;
export type HinhThucXuLyType = z.infer<typeof HinhThucXuLyEnum>;
export type ThuTucApDungType = z.infer<typeof ThuTucApDungEnum>;
export type TrangThaiGiaiQuyetType = z.infer<typeof TrangThaiGiaiQuyetEnum>;
