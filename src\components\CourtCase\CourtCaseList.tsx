"use client";

import { useState } from "react";
import { CourtCaseItemType } from "@/schemaValidations/courtCase.schema";
import { formatDate } from "@/utils/formatters";
import { usePermissions } from "@/hooks/usePermissions";
import { toast } from "react-toastify";

interface CourtCaseListProps {
  cases: CourtCaseItemType[];
  onCaseSelect: (courtCase: CourtCaseItemType) => void;
  onCaseEdit: (courtCase: CourtCaseItemType) => void;
  onCaseDelete: (caseId: string) => void;
  onBulkAction: (caseIds: string[], action: "delete") => void;
  loading?: boolean;
}

const CourtCaseList: React.FC<CourtCaseListProps> = ({
  cases,
  onCaseSelect,
  onCaseEdit,
  onCaseDelete,
  onBulkAction,
  loading = false
}) => {
  const { hasPermission } = usePermissions();
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedCases(cases.map(c => c._id));
    } else {
      setSelectedCases([]);
    }
  };

  const handleSelectCase = (caseId: string, checked: boolean) => {
    if (checked) {
      setSelectedCases(prev => [...prev, caseId]);
    } else {
      setSelectedCases(prev => prev.filter(id => id !== caseId));
      setSelectAll(false);
    }
  };

  const handleBulkDelete = () => {
    if (selectedCases.length === 0) {
      toast.warning("Vui lòng chọn ít nhất một vụ việc để xóa");
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedCases.length} vụ việc đã chọn?`)) {
      onBulkAction(selectedCases, "delete");
      setSelectedCases([]);
      setSelectAll(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Đã giải quyết':
        return 'bg-green-100 text-green-800';
      case 'Đang giải quyết':
        return 'bg-yellow-100 text-yellow-800';
      case 'Chưa giải quyết':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Hình sự': return '⚖️';
      case 'Dân sự': return '🏠';
      case 'Hành chính': return '🏛️';
      case 'Kinh tế': return '💼';
      case 'Lao động': return '👷';
      default: return '📋';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      {/* Bulk Actions */}
      {selectedCases.length > 0 && hasPermission('court_case_delete') && (
        <div className="bg-blue-50 px-6 py-3 border-b">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              Đã chọn {selectedCases.length} vụ việc
            </span>
            <div className="flex gap-2">
              <button
                onClick={handleBulkDelete}
                className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
              >
                Xóa đã chọn
              </button>
              <button
                onClick={() => {
                  setSelectedCases([]);
                  setSelectAll(false);
                }}
                className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
              >
                Bỏ chọn
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {hasPermission('court_case_delete') && (
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                </th>
              )}
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                STT
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Thông tin vụ việc
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Bị cáo/Người khiếu kiện
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Thẩm phán phụ trách
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trạng thái
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ngày thụ lý
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Thao tác
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {cases.length === 0 ? (
              <tr>
                <td colSpan={hasPermission('court_case_delete') ? 8 : 7} className="px-4 py-8 text-center text-gray-500">
                  Không có vụ việc nào
                </td>
              </tr>
            ) : (
              cases.map((courtCase) => (
                <tr key={courtCase._id} className="hover:bg-gray-50">
                  {hasPermission('court_case_delete') && (
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedCases.includes(courtCase._id)}
                        onChange={(e) => handleSelectCase(courtCase._id, e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </td>
                  )}
                  <td className="px-4 py-3 text-sm font-medium text-gray-900">
                    {courtCase.stt}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      <span className="text-2xl mr-3">
                        {getTypeIcon(courtCase.loaiAn)}
                      </span>
                      <div>
                        <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                          {courtCase.soThuLy}
                        </div>
                        <div className="text-xs text-gray-500">
                          {courtCase.loaiAn} - {courtCase.thuTucApDung}
                        </div>
                        <div className="text-xs text-gray-500 truncate max-w-xs">
                          {courtCase.tand}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <div className="truncate max-w-xs" title={courtCase.biCaoNguoiKhieuKien}>
                      {courtCase.biCaoNguoiKhieuKien}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <div className="truncate max-w-xs" title={courtCase.thamPhanPhuTrach}>
                      {courtCase.thamPhanPhuTrach}
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeClass(courtCase.trangThaiGiaiQuyet)}`}>
                      {courtCase.trangThaiGiaiQuyet}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    {formatDate(courtCase.ngayThuLy)}
                  </td>
                  <td className="px-4 py-3 text-sm font-medium">
                    <div className="flex gap-2">
                      {hasPermission('court_case_view') && (
                        <button
                          onClick={() => onCaseSelect(courtCase)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Xem chi tiết"
                        >
                          👁️
                        </button>
                      )}
                      {hasPermission('court_case_edit') && (
                        <button
                          onClick={() => onCaseEdit(courtCase)}
                          className="text-green-600 hover:text-green-900"
                          title="Chỉnh sửa"
                        >
                          ✏️
                        </button>
                      )}
                      {hasPermission('court_case_delete') && (
                        <button
                          onClick={() => onCaseDelete(courtCase._id)}
                          className="text-red-600 hover:text-red-900"
                          title="Xóa"
                        >
                          🗑️
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CourtCaseList;
