import http from '@/lib/http';
import { 
  CourtCaseType, 
  CourtCaseUpdateType, 
  CourtCaseItemType,
  CourtCaseSearchType,
  CourtCaseListResponseType,
  CourtCaseStatsType
} from '@/schemaValidations/courtCase.schema';

const courtCaseApiRequest = {
  // Get all court cases with pagination and filtering
  getCourtCases: (params: Partial<CourtCaseSearchType> = {}) => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const url = queryString ? `/api/court-cases?${queryString}` : '/api/court-cases';

    return http.get<CourtCaseListResponseType>(url);
  },

  // Get single court case by ID
  getCourtCaseById: (id: string) => {
    return http.get<{ success: boolean; case: CourtCaseItemType }>(`/api/court-cases/${id}`);
  },

  // Create new court case
  createCourtCase: (data: CourtCaseType) => {
    return http.post<{ success: boolean; message: string; case: CourtCaseItemType }>('/api/court-cases', data);
  },

  // Update court case
  updateCourtCase: (id: string, data: CourtCaseUpdateType) => {
    return http.put<{ success: boolean; message: string; case: CourtCaseItemType }>(`/api/court-cases/${id}`, data);
  },

  // Delete court case
  deleteCourtCase: (id: string) => {
    return http.delete<{ success: boolean; message: string }>(`/api/court-cases/${id}`);
  },

  // Get court case statistics
  getCourtCaseStats: () => {
    return http.get<CourtCaseStatsType>('/api/court-cases/stats');
  },

  // Bulk delete court cases
  bulkDeleteCourtCases: (ids: string[]) => {
    return http.post<{ success: boolean; message: string }>('/api/court-cases/bulk-delete', { ids });
  },

  // Export court cases to Excel/CSV
  exportCourtCases: async (params: Partial<CourtCaseSearchType> = {}) => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const url = queryString ? `/api/court-cases/export?${queryString}` : '/api/court-cases/export';

    // Use proper authentication headers like the http utility does
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add Authorization header if we have a session token
    if (typeof window !== 'undefined') {
      const sessionToken = localStorage.getItem("sessionToken");
      if (sessionToken) {
        headers['Authorization'] = `Bearer ${sessionToken}`;
      }
    }

    try {
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include', // Include cookies as fallback
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Export error:', response.status, errorText);
        throw new Error(`Export failed: ${response.status} ${response.statusText}`);
      }

      return {
        payload: await response.blob()
      };
    } catch (error) {
      console.error('Export request failed:', error);
      throw error;
    }
  },

  // Import court cases from Excel
  importCourtCases: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    // Use proper authentication headers like the http utility does
    const headers: HeadersInit = {};

    // Add Authorization header if we have a session token
    if (typeof window !== 'undefined') {
      const sessionToken = localStorage.getItem("sessionToken");
      if (sessionToken) {
        headers['Authorization'] = `Bearer ${sessionToken}`;
      }
    }

    try {
      const response = await fetch('/api/court-cases/import', {
        method: 'POST',
        credentials: 'include', // Include cookies as fallback
        headers,
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Import error:', response.status, errorText);
        throw new Error(`Import failed: ${response.status} ${response.statusText}`);
      }

      return {
        payload: await response.json()
      };
    } catch (error) {
      console.error('Import request failed:', error);
      throw error;
    }
  },

  // Get detailed statistics
  getDetailedStats: (params: { fromDate?: string; toDate?: string; groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year' } = {}) => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const url = queryString ? `/api/court-cases/detailed-stats?${queryString}` : '/api/court-cases/detailed-stats';

    return http.get<{
      success: boolean;
      stats: {
        summary: {
          total: number;
          period: string;
        };
        byStatus: Array<{ _id: string; count: number }>;
        byType: Array<{ _id: string; count: number }>;
        byProcedure: Array<{ _id: string; count: number }>;
        byProcessingMethod: Array<{ _id: string; count: number }>;
        trends: Array<{ _id: string; count: number; resolved: number; pending: number; inProgress: number }>;
        topJudges: Array<{ _id: string; count: number; resolved: number }>;
        byCourt: Array<{ _id: string; count: number }>;
      }
    }>(url);
  }
};

export default courtCaseApiRequest;
