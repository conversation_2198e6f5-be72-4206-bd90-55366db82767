"use client";
import Link from "next/link";
import { Menu } from "react-feather";
import ProfileDropdown from "@/components/Navigation/ProfileDropdown";
import { usePathname } from "next/navigation";

const Header: React.FC = () => {
  const pathname = usePathname();

  // Get page title based on current route
  const getPageTitle = () => {
    if (pathname === "/dashboard") return "Tổng quan";
    if (pathname.includes("/user")) return "Quản lý thành viên";
    if (pathname.includes("/files")) return "Quản lý file";
    if (pathname.includes("/court-cases")) return "Quản lý vụ việc tòa án";
    if (pathname.includes("/setting")) return "Cài đặt";
    if (pathname.includes("/account")) return "Thông tin tài khoản";
    return "Dashboard";
  };

  return (
    <header className="w-full bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left Section - Page Title & Breadcrumb */}
          <div className="flex items-center space-x-4">
            <div className="md:hidden">
              <button className="p-2 rounded-lg hover:bg-gray-100">
                <Menu size={20} className="text-gray-600" />
              </button>
            </div>

            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {getPageTitle()}
              </h1>
              <div className="flex items-center text-sm text-gray-500 mt-1">
                <span>Dashboard</span>
              </div>
            </div>
          </div>

          {/* Right Section - Profile */}
          <div className="flex items-center space-x-4">
            {/* Profile Dropdown */}
            <ProfileDropdown />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
