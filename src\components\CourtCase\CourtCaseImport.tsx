"use client";

import { useState, useRef } from "react";
import { toast } from "react-toastify";
import courtCaseApiRequest from "@/apiRequests/courtCase";

interface CourtCaseImportProps {
  onImportComplete: () => void;
  onClose: () => void;
}

const CourtCaseImport: React.FC<CourtCaseImportProps> = ({
  onImportComplete,
  onClose
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [importResults, setImportResults] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Check file type
      if (!selectedFile.name.endsWith('.xlsx') && !selectedFile.name.endsWith('.xls')) {
        toast.error('Vui lòng chọn file Excel (.xlsx hoặc .xls)');
        return;
      }
      setFile(selectedFile);
      setImportResults(null);
    }
  };

  const handleImport = async () => {
    if (!file) {
      toast.error('Vui lòng chọn file Excel');
      return;
    }

    try {
      setImporting(true);
      const response = await courtCaseApiRequest.importCourtCases(file);
      
      if (response.payload.success) {
        setImportResults(response.payload.results);
        toast.success(`Import thành công! ${response.payload.results.success}/${response.payload.results.total} vụ việc`);
        onImportComplete();
      } else {
        toast.error('Import thất bại');
      }
    } catch (error: any) {
      console.error('Error importing:', error);
      toast.error('Có lỗi xảy ra khi import file');
    } finally {
      setImporting(false);
    }
  };

  const downloadTemplate = () => {
    // Create a simple Excel template
    const headers = [
      'STT', 'Loại án', 'Số thụ lý', 'Ngày thụ lý', 'TAND',
      'Số bản án/QĐ', 'Ngày ban hành', 'Bị cáo/Người khiếu kiện',
      'Tội danh/Nội dung', 'Quan hệ pháp luật', 'Hình thức xử lý',
      'Thủ tục áp dụng', 'Thẩm phán phụ trách', 'Trưởng/Phó phòng KTNV',
      'Trạng thái', 'Ghi chú', 'Ghi chú kết quả'
    ];

    const csvContent = headers.join(',') + '\n' +
      '1,"Hình sự","123/2024","2024-01-15","TAND TP.HCM","456/2024","2024-02-01","Nguyễn Văn A","Trộm cắp tài sản","Điều 173 BLHS","Tuyên có tội - Tù","Sơ thẩm","Thẩm phán Nguyễn B","Trưởng phòng C","Đã giải quyết","","Tuyên 2 năm tù"';

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'mau-import-vu-viec.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
      <div 
        className="p-6 rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        style={{ backgroundColor: '#ffffff', color: '#111827' }}
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold" style={{ color: '#111827' }}>
            📥 Import vụ việc từ Excel
          </h2>
          <button
            onClick={onClose}
            className="text-xl font-bold p-2 rounded hover:bg-gray-100"
            style={{ color: '#6b7280' }}
          >
            ✕
          </button>
        </div>

        <div className="space-y-6">
          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2" style={{ color: '#111827' }}>📋 Hướng dẫn:</h3>
            <ul className="text-sm space-y-1" style={{ color: '#111827' }}>
              <li>• File Excel phải có định dạng .xlsx hoặc .xls</li>
              <li>• Dòng đầu tiên là tiêu đề cột (sẽ bị bỏ qua)</li>
              <li>• STT có thể để trống (hệ thống tự tạo)</li>
              <li>• Ngày tháng theo định dạng YYYY-MM-DD hoặc DD/MM/YYYY</li>
              <li>• Các trường khác có thể để trống</li>
            </ul>
          </div>

          {/* Template Download */}
          <div className="flex justify-between items-center p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 className="font-medium" style={{ color: '#111827' }}>📄 File mẫu</h4>
              <p className="text-sm text-gray-600">Tải xuống file mẫu để tham khảo định dạng</p>
            </div>
            <button
              onClick={downloadTemplate}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Tải file mẫu
            </button>
          </div>

          {/* File Selection */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                Chọn file Excel
              </label>
              <input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileSelect}
                className="w-full p-2 border border-gray-300 rounded-md"
                style={{ color: '#111827', backgroundColor: '#ffffff' }}
              />
            </div>

            {file && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm" style={{ color: '#111827' }}>
                  <strong>File đã chọn:</strong> {file.name}
                </p>
                <p className="text-sm text-gray-600">
                  Kích thước: {(file.size / 1024).toFixed(2)} KB
                </p>
              </div>
            )}
          </div>

          {/* Import Results */}
          {importResults && (
            <div className="space-y-3">
              <h4 className="font-medium" style={{ color: '#111827' }}>📊 Kết quả import:</h4>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-3 bg-blue-50 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">{importResults.total}</div>
                  <div className="text-sm text-gray-600">Tổng số dòng</div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">{importResults.success}</div>
                  <div className="text-sm text-gray-600">Thành công</div>
                </div>
                <div className="p-3 bg-yellow-50 rounded-lg text-center">
                  <div className="text-2xl font-bold text-yellow-600">{importResults.duplicates}</div>
                  <div className="text-sm text-gray-600">Trùng lặp</div>
                </div>
                <div className="p-3 bg-red-50 rounded-lg text-center">
                  <div className="text-2xl font-bold text-red-600">{importResults.errors.length}</div>
                  <div className="text-sm text-gray-600">Lỗi</div>
                </div>
              </div>

              {importResults.errors.length > 0 && (
                <div className="bg-red-50 p-4 rounded-lg">
                  <h5 className="font-medium text-red-800 mb-2">❌ Lỗi chi tiết:</h5>
                  <div className="max-h-32 overflow-y-auto">
                    {importResults.errors.map((error: string, index: number) => (
                      <p key={index} className="text-sm text-red-700">• {error}</p>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-4 pt-4 border-t">
            <button
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              style={{ color: '#111827' }}
              disabled={importing}
            >
              Đóng
            </button>
            <button
              onClick={handleImport}
              disabled={!file || importing}
              className="px-6 py-2 rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ backgroundColor: '#2563eb', color: '#ffffff' }}
            >
              {importing ? 'Đang import...' : '📥 Import'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourtCaseImport;
