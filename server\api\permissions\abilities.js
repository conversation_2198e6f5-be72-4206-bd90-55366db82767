const { AbilityBuilder, Ability, defineAbility } = require("@casl/ability");

let ANONYMOUS_ABILITY;

const defineAbilityFor = (user) => {
  if (user) {
    return new Ability(defineRulesFor(user));
  }
  ANONYMOUS_ABILITY = ANONYMOUS_ABILITY || new Ability(defineRulesFor({}));
  return ANONYMOUS_ABILITY;
};

function defineRulesFor(user) {
  const builder = new AbilityBuilder(Ability);
  switch (user.rule) {
    case "admin":
      defineAdminRules(builder, user);
      break;
    case "editor":
      defineEditorRules(builder, user);
      break;
    case "manager":
      defineManagerRules(builder, user);
      break;
    case "user":
      defineUserRules(builder, user);
      break;
    default:
      defineAnonymousRules(builder, user);
      break;
  }
  return builder.rules;
}

function defineAdminRules({ can }, user) {
  can("manage", "all"); // Admins can manage everything including files
  can("read", "dashboard"); // Explicitly allow dashboard access
}
function defineEditorRules({ can, cannot }, user) {
  can(["read", "update"], ["User"], { _id: user._id });
  can(["read", "update", "modify"], ["Post"], { user: user._id });
  can(["read", "update", "modify"], "Setting");
  
  // Check for specific permissions
  if (user.permissions) {
    // User management permissions
    if (user.permissions.includes('user_view')) {
      can("read", "User");
    }
    if (user.permissions.includes('user_add')) {
      can("create", "User");
    }
    if (user.permissions.includes('user_edit')) {
      can("update", "User");
    }
    if (user.permissions.includes('user_delete')) {
      can("delete", "User");
    }

    // File management permissions
    if (user.permissions.includes('file_view')) {
      can("read", "File");
    }
    if (user.permissions.includes('file_upload')) {
      can("create", "File");
    }
    if (user.permissions.includes('file_delete')) {
      can("delete", "File");
    }

    // Court case management permissions
    if (user.permissions.includes('court_case_view')) {
      can("read", "CourtCase");
    }
    if (user.permissions.includes('court_case_create')) {
      can("create", "CourtCase");
    }
    if (user.permissions.includes('court_case_edit')) {
      can("update", "CourtCase");
    }
    if (user.permissions.includes('court_case_delete')) {
      can("delete", "CourtCase");
    }
    if (user.permissions.includes('court_case_export')) {
      can("export", "CourtCase");
    }
    if (user.permissions.includes('court_case_import')) {
      can("import", "CourtCase");
    }
    if (user.permissions.includes('court_case_stats_view')) {
      can("read", "CourtCaseStats");
    }
    if (user.permissions.includes('court_case_detailed_stats_view')) {
      can("read", "CourtCaseDetailedStats");
    }

    // Court Case User Account Management permissions
    if (user.permissions.includes('court_case_user_profile_view')) {
      can("read", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_profile_edit')) {
      can("update", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_password_change')) {
      can("changePassword", "User");
    }
    if (user.permissions.includes('court_case_user_permissions_view')) {
      can("read", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_permissions_edit')) {
      can("update", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_activity_log_view')) {
      can("read", "UserActivityLog");
    }
    if (user.permissions.includes('court_case_user_two_factor_manage')) {
      can(["read", "update"], "UserTwoFactor");
    }
    
    // Dashboard access if user has analytics_view permission
    if (user.permissions.includes('analytics_view')) {
      can("read", "dashboard");
    }
  }
}

function defineManagerRules({ can, cannot }, user) {
  can(["read", "update"], ["User"], { _id: user._id });
  can(["read", "update", "modify"], ["Post"]);
  can(["read", "create", "update", "delete"], "File"); // Managers can manage files
  can(["read", "create", "update", "delete"], "CourtCase"); // Managers can manage court cases
  
  // Check for specific permissions
  if (user.permissions) {
    // User management permissions
    if (user.permissions.includes('user_view')) {
      can("read", "User");
    }
    if (user.permissions.includes('user_add')) {
      can("create", "User");
    }
    if (user.permissions.includes('user_edit')) {
      can("update", "User");
    }
    if (user.permissions.includes('user_delete')) {
      can("delete", "User");
    }

    // File management permissions
    if (user.permissions.includes('file_view')) {
      can("read", "File");
    }
    if (user.permissions.includes('file_upload')) {
      can("create", "File");
    }
    if (user.permissions.includes('file_delete')) {
      can("delete", "File");
    }

    // Court case management permissions
    if (user.permissions.includes('court_case_view')) {
      can("read", "CourtCase");
    }
    if (user.permissions.includes('court_case_create')) {
      can("create", "CourtCase");
    }
    if (user.permissions.includes('court_case_edit')) {
      can("update", "CourtCase");
    }
    if (user.permissions.includes('court_case_delete')) {
      can("delete", "CourtCase");
    }
    if (user.permissions.includes('court_case_export')) {
      can("export", "CourtCase");
    }
    if (user.permissions.includes('court_case_import')) {
      can("import", "CourtCase");
    }
    if (user.permissions.includes('court_case_stats_view')) {
      can("read", "CourtCaseStats");
    }
    if (user.permissions.includes('court_case_detailed_stats_view')) {
      can("read", "CourtCaseDetailedStats");
    }

    // Court Case User Account Management permissions
    if (user.permissions.includes('court_case_user_profile_view')) {
      can("read", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_profile_edit')) {
      can("update", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_password_change')) {
      can("changePassword", "User");
    }
    if (user.permissions.includes('court_case_user_permissions_view')) {
      can("read", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_permissions_edit')) {
      can("update", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_activity_log_view')) {
      can("read", "UserActivityLog");
    }
    if (user.permissions.includes('court_case_user_two_factor_manage')) {
      can(["read", "update"], "UserTwoFactor");
    }
    
    // Dashboard access if user has analytics_view permission
    if (user.permissions.includes('analytics_view')) {
      can("read", "dashboard");
    }
  }
}

function defineUserRules({ can, cannot }, user) {
  can(["read", "update"], ["User"], { _id: user._id });
  can(["read", "update", "modify"], ["Post"], { user: user._id });
  
  // Check for specific permissions
  if (user.permissions) {
    // User management permissions
    if (user.permissions.includes('user_view')) {
      can("read", "User");
    }
    if (user.permissions.includes('user_add')) {
      can("create", "User");
    }
    if (user.permissions.includes('user_edit')) {
      can("update", "User");
    }
    if (user.permissions.includes('user_delete')) {
      can("delete", "User");
    }
    
    // File management permissions
    if (user.permissions.includes('file_view')) {
      can("read", "File");
    }
    if (user.permissions.includes('file_upload')) {
      can("create", "File");
    }
    if (user.permissions.includes('file_delete')) {
      can("delete", "File");
    }

    // Court case management permissions
    if (user.permissions.includes('court_case_view')) {
      can("read", "CourtCase");
    }
    if (user.permissions.includes('court_case_create')) {
      can("create", "CourtCase");
    }
    if (user.permissions.includes('court_case_edit')) {
      can("update", "CourtCase");
    }
    if (user.permissions.includes('court_case_delete')) {
      can("delete", "CourtCase");
    }
    if (user.permissions.includes('court_case_export')) {
      can("export", "CourtCase");
    }
    if (user.permissions.includes('court_case_import')) {
      can("import", "CourtCase");
    }
    if (user.permissions.includes('court_case_stats_view')) {
      can("read", "CourtCaseStats");
    }
    if (user.permissions.includes('court_case_detailed_stats_view')) {
      can("read", "CourtCaseDetailedStats");
    }

    // Court Case User Account Management permissions
    if (user.permissions.includes('court_case_user_profile_view')) {
      can("read", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_profile_edit')) {
      can("update", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_password_change')) {
      can("changePassword", "User");
    }
    if (user.permissions.includes('court_case_user_permissions_view')) {
      can("read", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_permissions_edit')) {
      can("update", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_activity_log_view')) {
      can("read", "UserActivityLog");
    }
    if (user.permissions.includes('court_case_user_two_factor_manage')) {
      can(["read", "update"], "UserTwoFactor");
    }

    // Dashboard access if user has analytics_view permission
    if (user.permissions.includes('analytics_view')) {
      can("read", "dashboard");
    }
  }
}

function defineAnonymousRules({ can }) {
  can("read", ["User"]);
}

module.exports = { defineAbilityFor };
