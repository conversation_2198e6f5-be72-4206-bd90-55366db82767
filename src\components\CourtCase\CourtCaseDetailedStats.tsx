"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import courtCaseApiRequest from "@/apiRequests/courtCase";

interface DetailedStatsProps {
  onClose: () => void;
}

const CourtCaseDetailedStats: React.FC<DetailedStatsProps> = ({ onClose }) => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    fromDate: '',
    toDate: '',
    groupBy: 'month' as 'day' | 'week' | 'month' | 'quarter' | 'year'
  });

  const fetchDetailedStats = async () => {
    try {
      setLoading(true);
      const response = await courtCaseApiRequest.getDetailedStats(filters);
      
      if (response.payload.success) {
        setStats(response.payload.stats);
      } else {
        toast.error('Không thể tải thống kê chi tiết');
      }
    } catch (error) {
      console.error('Error fetching detailed stats:', error);
      toast.error('C<PERSON> lỗi xảy ra khi tải thống kê');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetailedStats();
  }, [filters]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Đã giải quyết': return 'text-green-600 bg-green-100';
      case 'Đang giải quyết': return 'text-yellow-600 bg-yellow-100';
      case 'Chưa giải quyết': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const exportDetailedReport = async () => {
    try {
      toast.info('Đang xuất báo cáo chi tiết...');
      const response = await courtCaseApiRequest.exportCourtCases(filters);
      
      // Create blob and download
      const blob = new Blob([response.payload], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `bao-cao-chi-tiet-vu-viec-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('Xuất báo cáo thành công');
    } catch (error) {
      console.error('Error exporting detailed report:', error);
      toast.error('Có lỗi xảy ra khi xuất báo cáo');
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
      <div 
        className="p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        style={{ backgroundColor: '#ffffff', color: '#111827' }}
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold" style={{ color: '#111827' }}>
            📊 Thống kê chi tiết vụ việc
          </h2>
          <button
            onClick={onClose}
            className="text-xl font-bold p-2 rounded hover:bg-gray-100"
            style={{ color: '#6b7280' }}
          >
            ✕
          </button>
        </div>

        {/* Filters */}
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-semibold mb-4" style={{ color: '#111827' }}>🔍 Bộ lọc</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                Từ ngày
              </label>
              <input
                type="date"
                value={filters.fromDate}
                onChange={(e) => handleFilterChange('fromDate', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                style={{ color: '#111827', backgroundColor: '#ffffff' }}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                Đến ngày
              </label>
              <input
                type="date"
                value={filters.toDate}
                onChange={(e) => handleFilterChange('toDate', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                style={{ color: '#111827', backgroundColor: '#ffffff' }}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: '#111827' }}>
                Nhóm theo
              </label>
              <select
                value={filters.groupBy}
                onChange={(e) => handleFilterChange('groupBy', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                style={{ color: '#111827', backgroundColor: '#ffffff' }}
              >
                <option value="day">Ngày</option>
                <option value="week">Tuần</option>
                <option value="month">Tháng</option>
                <option value="quarter">Quý</option>
                <option value="year">Năm</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={exportDetailedReport}
                className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                📊 Xuất báo cáo
              </button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Đang tải thống kê...</p>
          </div>
        ) : stats ? (
          <div className="space-y-6">
            {/* Summary */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2" style={{ color: '#111827' }}>📋 Tổng quan</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-600">Tổng số vụ việc:</span>
                  <span className="ml-2 font-bold text-2xl text-blue-600">{stats.summary.total}</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">Thời gian:</span>
                  <span className="ml-2 font-medium">{stats.summary.period}</span>
                </div>
              </div>
            </div>

            {/* Status Distribution */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="font-semibold mb-4" style={{ color: '#111827' }}>📈 Phân bố theo trạng thái</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {stats.byStatus.map((item: any) => (
                  <div key={item._id} className={`p-4 rounded-lg ${getStatusColor(item._id)}`}>
                    <div className="text-2xl font-bold">{item.count}</div>
                    <div className="text-sm">{item._id || 'Không xác định'}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Case Type Distribution */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="font-semibold mb-4" style={{ color: '#111827' }}>⚖️ Phân bố theo loại án</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                {stats.byType.map((item: any) => (
                  <div key={item._id} className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-xl font-bold text-gray-700">{item.count}</div>
                    <div className="text-sm text-gray-600">{item._id || 'Khác'}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Processing Method Distribution */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="font-semibold mb-4" style={{ color: '#111827' }}>🔨 Phân bố theo hình thức xử lý</h3>
              <div className="space-y-2">
                {stats.byProcessingMethod.slice(0, 8).map((item: any) => (
                  <div key={item._id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="text-sm">{item._id || 'Không xác định'}</span>
                    <span className="font-bold text-blue-600">{item.count}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Judges */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="font-semibold mb-4" style={{ color: '#111827' }}>👨‍⚖️ Top thẩm phán (theo số vụ việc)</h3>
              <div className="space-y-2">
                {stats.topJudges.slice(0, 10).map((judge: any, index: number) => (
                  <div key={judge._id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <div className="flex items-center">
                      <span className="w-6 h-6 bg-blue-600 text-white rounded-full text-xs flex items-center justify-center mr-3">
                        {index + 1}
                      </span>
                      <span className="text-sm">{judge._id}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-blue-600">{judge.count} vụ</div>
                      <div className="text-xs text-green-600">{judge.resolved} đã giải quyết</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Court Distribution */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="font-semibold mb-4" style={{ color: '#111827' }}>🏛️ Phân bố theo tòa án</h3>
              <div className="space-y-2">
                {stats.byCourt.slice(0, 10).map((court: any) => (
                  <div key={court._id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="text-sm">{court._id || 'Không xác định'}</span>
                    <span className="font-bold text-purple-600">{court.count}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Trends */}
            <div className="bg-white border rounded-lg p-4">
              <h3 className="font-semibold mb-4" style={{ color: '#111827' }}>📈 Xu hướng theo thời gian</h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {stats.trends.map((trend: any) => (
                  <div key={trend._id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="text-sm font-medium">{trend._id}</span>
                    <div className="flex gap-4 text-sm">
                      <span className="text-blue-600">Tổng: {trend.count}</span>
                      <span className="text-green-600">Đã giải quyết: {trend.resolved}</span>
                      <span className="text-yellow-600">Đang giải quyết: {trend.inProgress}</span>
                      <span className="text-red-600">Chưa giải quyết: {trend.pending}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            Không có dữ liệu thống kê
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-4 pt-6 border-t mt-6">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            style={{ color: '#111827' }}
          >
            Đóng
          </button>
        </div>
      </div>
    </div>
  );
};

export default CourtCaseDetailedStats;
