import http from '@/lib/http';
import { 
  CourtCaseUserProfileResponseType,
  CourtCaseUserProfileUpdateType,
  CourtCaseUserPasswordChangeType,
  CourtCaseUserPermissionsResponseType,
  CourtCaseUserPermissionsUpdateType,
  CourtCaseUserActivityLogsResponseType,
  CourtCaseUserTwoFactorResponseType,
  CourtCaseUserTwoFactorUpdateType,
  CourtCaseUserGenericResponseType
} from '@/schemaValidations/courtCaseUserManagement.schema';

const courtCaseUserManagementApiRequest = {
  // User Profile Management
  
  // Get user profile (current user if no userId provided)
  getUserProfile: (userId?: string) => {
    const url = userId 
      ? `/api/court-cases/user-management/profile/${userId}`
      : '/api/court-cases/user-management/profile';
    return http.get<CourtCaseUserProfileResponseType>(url);
  },

  // Update user profile (current user if no userId provided)
  updateUserProfile: (data: CourtCaseUserProfileUpdateType, userId?: string) => {
    const url = userId 
      ? `/api/court-cases/user-management/profile/${userId}`
      : '/api/court-cases/user-management/profile';
    return http.put<CourtCaseUserGenericResponseType>(url, data);
  },

  // Change user password (current user if no userId provided)
  changeUserPassword: (data: CourtCaseUserPasswordChangeType, userId?: string) => {
    const url = userId 
      ? `/api/court-cases/user-management/change-password/${userId}`
      : '/api/court-cases/user-management/change-password';
    return http.post<CourtCaseUserGenericResponseType>(url, data);
  },

  // User Permissions Management
  
  // Get user permissions
  getUserPermissions: (userId: string) => {
    return http.get<CourtCaseUserPermissionsResponseType>(`/api/court-cases/user-management/permissions/${userId}`);
  },

  // Update user permissions
  updateUserPermissions: (userId: string, data: CourtCaseUserPermissionsUpdateType) => {
    return http.put<CourtCaseUserGenericResponseType>(`/api/court-cases/user-management/permissions/${userId}`, data);
  },

  // User Activity Logs
  
  // Get user activity logs
  getUserActivityLogs: (userId: string, params: { page?: number; limit?: number } = {}) => {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const url = queryString 
      ? `/api/court-cases/user-management/activity-logs/${userId}?${queryString}`
      : `/api/court-cases/user-management/activity-logs/${userId}`;

    return http.get<CourtCaseUserActivityLogsResponseType>(url);
  },

  // Two-Factor Authentication Management
  
  // Get two-factor authentication status (current user if no userId provided)
  getTwoFactorStatus: (userId?: string) => {
    const url = userId 
      ? `/api/court-cases/user-management/two-factor/${userId}`
      : '/api/court-cases/user-management/two-factor';
    return http.get<CourtCaseUserTwoFactorResponseType>(url);
  },

  // Update two-factor authentication settings (current user if no userId provided)
  updateTwoFactorSettings: (data: CourtCaseUserTwoFactorUpdateType, userId?: string) => {
    const url = userId 
      ? `/api/court-cases/user-management/two-factor/${userId}`
      : '/api/court-cases/user-management/two-factor';
    return http.put<CourtCaseUserGenericResponseType>(url, data);
  }
};

export default courtCaseUserManagementApiRequest;
