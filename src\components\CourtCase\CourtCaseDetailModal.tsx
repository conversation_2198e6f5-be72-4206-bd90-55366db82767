"use client";

import { CourtCaseItemType } from "@/schemaValidations/courtCase.schema";
import { formatDate } from "@/utils/formatters";
import { usePermissions } from "@/hooks/usePermissions";

interface CourtCaseDetailModalProps {
  courtCase: CourtCaseItemType;
  onClose: () => void;
  onEdit: (courtCase: CourtCaseItemType) => void;
  onDelete: (caseId: string) => void;
}

const CourtCaseDetailModal: React.FC<CourtCaseDetailModalProps> = ({
  courtCase,
  onClose,
  onEdit,
  onDelete
}) => {
  const { hasPermission } = usePermissions();
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Đã giải quyết':
        return 'bg-green-100 text-green-800';
      case 'Đang giải quyết':
        return 'bg-yellow-100 text-yellow-800';
      case 'Chưa giải quyết':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Hình sự': return '⚖️';
      case 'Dân sự': return '🏠';
      case 'Hành chính': return '🏛️';
      case 'Kinh tế': return '💼';
      case 'Lao động': return '👷';
      default: return '📋';
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
      <div 
        className="p-6 rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        style={{ backgroundColor: '#ffffff', color: '#111827' }}
      >
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <span className="text-3xl mr-3">{getTypeIcon(courtCase.loaiAn)}</span>
            <div>
              <h2 className="text-2xl font-bold" style={{ color: '#111827' }}>
                Chi tiết vụ việc #{courtCase.stt}
              </h2>
              <p className="text-gray-600">{courtCase.soThuLy}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-xl font-bold p-2 rounded hover:bg-gray-100"
            style={{ color: '#6b7280' }}
          >
            ✕
          </button>
        </div>

        <div className="space-y-6">
          {/* Thông tin thụ lý */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ color: '#111827' }}>
              📋 Thông tin thụ lý
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600">STT</label>
                <p className="text-lg font-semibold" style={{ color: '#111827' }}>{courtCase.stt}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Loại án</label>
                <p className="text-lg" style={{ color: '#111827' }}>{courtCase.loaiAn}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Số thụ lý</label>
                <p className="text-lg font-mono" style={{ color: '#111827' }}>{courtCase.soThuLy}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Ngày thụ lý</label>
                <p className="text-lg" style={{ color: '#111827' }}>{formatDate(courtCase.ngayThuLy)}</p>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-600">TAND</label>
                <p className="text-lg" style={{ color: '#111827' }}>{courtCase.tand}</p>
              </div>
            </div>
          </div>

          {/* Thông tin bản án/quyết định */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ color: '#111827' }}>
              ⚖️ Thông tin bản án/quyết định
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600">Số bản án/quyết định</label>
                <p className="text-lg font-mono" style={{ color: '#111827' }}>{courtCase.soBanAn}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Ngày ban hành</label>
                <p className="text-lg" style={{ color: '#111827' }}>{formatDate(courtCase.ngayBanHanh)}</p>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-600">Bị cáo/Nguyên đơn/Người khiếu kiện</label>
                <p className="text-lg" style={{ color: '#111827' }}>{courtCase.biCaoNguoiKhieuKien}</p>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-600">Tội danh/Bồi dưỡng/Nội dung khiếu kiện</label>
                <p className="text-lg" style={{ color: '#111827' }}>{courtCase.toiDanhNoiDung}</p>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-600">Tội danh/Quan hệ pháp luật</label>
                <p className="text-lg" style={{ color: '#111827' }}>{courtCase.quanHePhatLuat}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Hình thức xử lý</label>
                <p className="text-lg" style={{ color: '#111827' }}>{courtCase.hinhThucXuLy}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Thủ tục áp dụng</label>
                <p className="text-lg" style={{ color: '#111827' }}>{courtCase.thuTucApDung}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Thẩm phán phụ trách</label>
                <p className="text-lg" style={{ color: '#111827' }}>{courtCase.thamPhanPhuTrach}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Trưởng/Phó phòng KTNV/Thẩm tra viên</label>
                <p className="text-lg" style={{ color: '#111827' }}>{courtCase.truongPhoPhongKTNV}</p>
              </div>
            </div>
          </div>

          {/* Thông tin bổ sung */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ color: '#111827' }}>
              📝 Thông tin bổ sung
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600">Trạng thái giải quyết</label>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusBadgeClass(courtCase.trangThaiGiaiQuyet)}`}>
                  {courtCase.trangThaiGiaiQuyet}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Ngày cập nhật cuối</label>
                <p className="text-lg" style={{ color: '#111827' }}>{formatDate(courtCase.updatedAt)}</p>
              </div>
              {courtCase.ghiChu && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-600">Ghi chú</label>
                  <p className="text-lg" style={{ color: '#111827' }}>{courtCase.ghiChu}</p>
                </div>
              )}
              {courtCase.ghiChuKetQua && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-600">Ghi chú kết quả</label>
                  <p className="text-lg" style={{ color: '#111827' }}>{courtCase.ghiChuKetQua}</p>
                </div>
              )}
            </div>
          </div>

          {/* Thông tin người tạo/cập nhật */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ color: '#111827' }}>
              👤 Thông tin hệ thống
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600">Người tạo</label>
                <p className="text-lg" style={{ color: '#111827' }}>
                  {courtCase.createdBy?.username || 'N/A'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600">Ngày tạo</label>
                <p className="text-lg" style={{ color: '#111827' }}>{formatDate(courtCase.createdAt)}</p>
              </div>
              {courtCase.updatedBy && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-600">Người cập nhật cuối</label>
                    <p className="text-lg" style={{ color: '#111827' }}>
                      {courtCase.updatedBy.username}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600">Ngày cập nhật cuối</label>
                    <p className="text-lg" style={{ color: '#111827' }}>{formatDate(courtCase.updatedAt)}</p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 pt-6 border-t mt-6">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            style={{ color: '#111827' }}
          >
            Đóng
          </button>
          {hasPermission('court_case_edit') && (
            <button
              onClick={() => {
                onClose(); // Đóng modal chi tiết trước
                onEdit(courtCase); // Sau đó mở form chỉnh sửa
              }}
              className="px-6 py-2 rounded-md hover:opacity-90 transition-opacity"
              style={{ backgroundColor: '#059669', color: '#ffffff' }}
            >
              Chỉnh sửa
            </button>
          )}
          {hasPermission('court_case_delete') && (
            <button
              onClick={() => {
                if (confirm('Bạn có chắc chắn muốn xóa vụ việc này?')) {
                  onDelete(courtCase._id);
                  onClose();
                }
              }}
              className="px-6 py-2 rounded-md hover:opacity-90 transition-opacity"
              style={{ backgroundColor: '#dc2626', color: '#ffffff' }}
            >
              Xóa
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CourtCaseDetailModal;
