const File = require('../models/file');
const User = require('../models/user');
const { defineAbilityFor } = require('../permissions/abilities');
const { ForbiddenError } = require('@casl/ability');
const fs = require('fs');
const path = require('path');

// Get all files with pagination and filters
exports.getFiles = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    // Check if user can read files
    ForbiddenError.from(ability).throwUnlessCan('read', 'File');

    const { 
      page = 1, 
      perPage = 20, 
      type = 'all', 
      sortBy = 'createdAt', 
      sortOrder = 'desc',
      query = ''
    } = req.body;

    // Build filter
    let filter = { isActive: true };
    
    if (type !== 'all') {
      filter.type = type;
    }

    if (query) {
      filter.$text = { $search: query };
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute queries
    const skip = (page - 1) * perPage;
    
    const [files, total] = await Promise.all([
      File.find(filter)
        .populate('uploadedBy', 'username email')
        .sort(sort)
        .skip(skip)
        .limit(perPage)
        .lean(),
      File.countDocuments(filter)
    ]);

    // Format files with URLs
    const formattedFiles = files.map(file => ({
      ...file,
      url: `/api/files/serve/${file._id}`,
      uploadedAt: file.createdAt
    }));

    res.json({
      success: true,
      files: formattedFiles,
      total,
      page: parseInt(page),
      perPage: parseInt(perPage)
    });

  } catch (error) {
    console.error('Error getting files:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get single file by ID
exports.getFileById = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { id } = req.params;
    
    const file = await File.findById(id)
      .populate('uploadedBy', 'username email')
      .lean();

    if (!file) {
      return res.status(404).json({ success: false, message: 'File not found' });
    }

    ForbiddenError.from(ability).throwUnlessCan('read', file);

    res.json({
      success: true,
      file: {
        ...file,
        url: `/api/files/serve/${file._id}`,
        uploadedAt: file.createdAt
      }
    });

  } catch (error) {
    console.error('Error getting file:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Delete single file
exports.deleteFile = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { id } = req.params;
    
    const file = await File.findById(id);
    if (!file) {
      return res.status(404).json({ success: false, message: 'File not found' });
    }

    ForbiddenError.from(ability).throwUnlessCan('delete', file);

    // Delete physical file
    try {
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
    } catch (fsError) {
      console.error('Error deleting physical file:', fsError);
    }

    // Delete from database
    await File.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting file:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Bulk actions on files
exports.bulkAction = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { fileIds, action } = req.body;

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return res.status(400).json({ success: false, message: 'File IDs are required' });
    }

    const files = await File.find({ _id: { $in: fileIds } });
    
    // Check permissions for all files
    files.forEach(file => {
      if (action === 'delete') {
        ForbiddenError.from(ability).throwUnlessCan('delete', file);
      } else {
        ForbiddenError.from(ability).throwUnlessCan('update', file);
      }
    });

    let result;
    switch (action) {
      case 'delete':
        // Delete physical files
        for (const file of files) {
          try {
            if (fs.existsSync(file.path)) {
              fs.unlinkSync(file.path);
            }
          } catch (fsError) {
            console.error('Error deleting physical file:', fsError);
          }
        }
        result = await File.deleteMany({ _id: { $in: fileIds } });
        break;
        
      case 'activate':
        result = await File.updateMany(
          { _id: { $in: fileIds } },
          { isActive: true }
        );
        break;
        
      case 'deactivate':
        result = await File.updateMany(
          { _id: { $in: fileIds } },
          { isActive: false }
        );
        break;
        
      default:
        return res.status(400).json({ success: false, message: 'Invalid action' });
    }

    res.json({
      success: true,
      message: `${action} completed successfully`,
      affected: result.modifiedCount || result.deletedCount
    });

  } catch (error) {
    console.error('Error in bulk action:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Update file metadata
exports.updateFile = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { id } = req.params;
    const { description, tags, isActive } = req.body;
    
    const file = await File.findById(id);
    if (!file) {
      return res.status(404).json({ success: false, message: 'File not found' });
    }

    ForbiddenError.from(ability).throwUnlessCan('update', file);

    const updateData = {};
    if (description !== undefined) updateData.description = description;
    if (tags !== undefined) updateData.tags = tags;
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedFile = await File.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('uploadedBy', 'username email');

    res.json({
      success: true,
      file: updatedFile,
      message: 'File updated successfully'
    });

  } catch (error) {
    console.error('Error updating file:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get file statistics
exports.getFileStats = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'File');

    const stats = await File.getStats();

    res.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Error getting file stats:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Search files
exports.searchFiles = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'File');

    const { q } = req.query;

    if (!q) {
      return res.status(400).json({ success: false, message: 'Search query is required' });
    }

    const files = await File.find({
      isActive: true,
      $text: { $search: q }
    })
    .populate('uploadedBy', 'username email')
    .sort({ score: { $meta: 'textScore' } })
    .limit(50)
    .lean();

    const formattedFiles = files.map(file => ({
      ...file,
      url: `/api/files/serve/${file._id}`,
      uploadedAt: file.createdAt
    }));

    res.json({
      success: true,
      files: formattedFiles,
      total: files.length
    });

  } catch (error) {
    console.error('Error searching files:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Serve file (for download/view)
exports.serveFile = async (req, res) => {
  try {
    const { id } = req.params;

    const file = await File.findById(id);
    if (!file || !file.isActive) {
      return res.status(404).json({ success: false, message: 'File not found' });
    }

    // Update download count and last accessed
    await File.findByIdAndUpdate(id, {
      $inc: { downloadCount: 1 },
      lastAccessed: new Date()
    });

    // Check if file exists
    if (!fs.existsSync(file.path)) {
      return res.status(404).json({ success: false, message: 'Physical file not found' });
    }

    // Set appropriate headers
    res.setHeader('Content-Type', file.mimetype);
    res.setHeader('Content-Disposition', `inline; filename="${file.originalName}"`);

    // Stream the file
    const fileStream = fs.createReadStream(file.path);
    fileStream.pipe(res);

  } catch (error) {
    console.error('Error serving file:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Sync existing files from uploads directory
exports.syncExistingFiles = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    // Only admin can sync files
    ForbiddenError.from(ability).throwUnlessCan('manage', 'File');

    const { syncExistingFiles } = require('../../sync-existing-files');
    
    // Run sync in background and return immediate response
    res.json({
      success: true,
      message: 'File synchronization started. Check server logs for progress.'
    });

    // Run sync asynchronously
    syncExistingFiles().catch(error => {
      console.error('Error in file sync:', error);
    });

  } catch (error) {
    console.error('Error starting file sync:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied. Admin privileges required.' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get sync status and existing files not in database
exports.getSyncStatus = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'File');

    const fs = require('fs');
    const path = require('path');
    
    // Function to scan directory recursively
    const scanDirectory = (dir, baseDir = dir) => {
      let files = [];
      
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            files = files.concat(scanDirectory(fullPath, baseDir));
          } else if (stat.isFile()) {
            const relativePath = path.relative(baseDir, fullPath).replace(/\\/g, '/');
            files.push({
              relativePath,
              filename: item,
              size: stat.size,
              mtime: stat.mtime
            });
          }
        }
      } catch (error) {
        console.error(`Error scanning directory ${dir}:`, error.message);
      }
      
      return files;
    };

    // Scan uploads directory
    const uploadsDir = path.join(__dirname, '../../uploads');
    const physicalFiles = fs.existsSync(uploadsDir) ? scanDirectory(uploadsDir) : [];
    
    // Get files in database
    const dbFiles = await File.find({}).lean();
    const dbPaths = new Set(dbFiles.map(f => {
      // Extract relative path from various formats
      let relativePath = f.relativePath || f.path || '';

      // Remove leading slash and uploads/ prefix
      relativePath = relativePath.replace(/^\/+/, ''); // Remove leading slashes
      relativePath = relativePath.replace(/^uploads\//, ''); // Remove uploads/ prefix
      relativePath = relativePath.replace(/\\/g, '/'); // Normalize slashes

      return relativePath;
    }));
    
    // Find files not in database
    const missingFiles = physicalFiles.filter(file => 
      !dbPaths.has(file.relativePath)
    );

    res.json({
      success: true,
      status: {
        totalPhysicalFiles: physicalFiles.length,
        totalDatabaseFiles: dbFiles.length,
        missingInDatabase: missingFiles.length,
        missingFiles: missingFiles.slice(0, 20) // Return first 20 for preview
      }
    });

  } catch (error) {
    console.error('Error getting sync status:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};
