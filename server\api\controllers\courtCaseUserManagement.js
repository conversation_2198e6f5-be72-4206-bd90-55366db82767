const User = require('../models/user');
const { defineAbilityFor } = require('../permissions/abilities');
const { ForbiddenError } = require('@casl/ability');
const bcrypt = require('bcryptjs');

// Get user profile information for court case management
exports.getUserProfile = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'UserProfile');

    const { userId } = req.params;
    const targetUserId = userId || req.user._id;

    const user = await User.findById(targetUserId)
      .select('-password -resetlink -twoFactorSecret')
      .populate('address');

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Check if user can view this specific user's profile
    if (userId && userId !== req.user._id.toString()) {
      ForbiddenError.from(ability).throwUnlessCan('read', 'User');
    }

    res.json({
      success: true,
      user: {
        _id: user._id,
        username: user.username,
        email: user.email,
        phonenumber: user.phonenumber,
        rule: user.rule,
        permissions: user.permissions,
        avatar: user.avatar,
        address: user.address,
        bio: user.bio,
        gender: user.gender,
        rank: user.rank,
        point: user.point,
        isMail: user.isMail,
        isAuthApp: user.isAuthApp,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting user profile:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Update user profile for court case management
exports.updateUserProfile = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('update', 'UserProfile');

    const { userId } = req.params;
    const targetUserId = userId || req.user._id;

    // Check if user can edit this specific user's profile
    if (userId && userId !== req.user._id.toString()) {
      ForbiddenError.from(ability).throwUnlessCan('update', 'User');
    }

    const {
      username,
      email,
      phonenumber,
      bio,
      gender,
      avatar
    } = req.body;

    const updateData = {};
    if (username !== undefined) updateData.username = username;
    if (email !== undefined) updateData.email = email;
    if (phonenumber !== undefined) updateData.phonenumber = phonenumber;
    if (bio !== undefined) updateData.bio = bio;
    if (gender !== undefined) updateData.gender = gender;
    if (avatar !== undefined) updateData.avatar = avatar;

    const user = await User.findByIdAndUpdate(
      targetUserId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -resetlink -twoFactorSecret');

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    res.json({
      success: true,
      message: 'Profile updated successfully',
      user
    });

  } catch (error) {
    console.error('Error updating user profile:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else if (error.code === 11000) {
      res.status(400).json({ success: false, message: 'Email or phone number already exists' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Change user password for court case management
exports.changeUserPassword = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('changePassword', 'User');

    const { userId } = req.params;
    const targetUserId = userId || req.user._id;
    const { currentPassword, newPassword, confirmPassword } = req.body;

    // Validate input
    if (!newPassword || !confirmPassword) {
      return res.status(400).json({ success: false, message: 'New password and confirmation are required' });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({ success: false, message: 'New password and confirmation do not match' });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ success: false, message: 'Password must be at least 6 characters long' });
    }

    const user = await User.findById(targetUserId);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // If changing own password, verify current password
    if (targetUserId === req.user._id.toString()) {
      if (!currentPassword) {
        return res.status(400).json({ success: false, message: 'Current password is required' });
      }
      
      const isCurrentPasswordValid = user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({ success: false, message: 'Current password is incorrect' });
      }
    } else {
      // Check if user can change other user's password
      ForbiddenError.from(ability).throwUnlessCan('update', 'User');
    }

    // Hash new password
    const saltRounds = 10;
    const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);

    await User.findByIdAndUpdate(targetUserId, { password: hashedPassword });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });

  } catch (error) {
    console.error('Error changing password:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get user permissions for court case management
exports.getUserPermissions = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'UserPermissions');

    const { userId } = req.params;

    const user = await User.findById(userId).select('username email rule permissions');
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    res.json({
      success: true,
      user: {
        _id: user._id,
        username: user.username,
        email: user.email,
        rule: user.rule,
        permissions: user.permissions
      }
    });

  } catch (error) {
    console.error('Error getting user permissions:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Update user permissions for court case management
exports.updateUserPermissions = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('update', 'UserPermissions');

    const { userId } = req.params;
    const { permissions, rule } = req.body;

    const updateData = {};
    if (permissions !== undefined) updateData.permissions = permissions;
    if (rule !== undefined) updateData.rule = rule;

    const user = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    ).select('username email rule permissions');

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    res.json({
      success: true,
      message: 'User permissions updated successfully',
      user
    });

  } catch (error) {
    console.error('Error updating user permissions:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get user activity logs (placeholder - would require activity logging implementation)
exports.getUserActivityLogs = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'UserActivityLog');

    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    // This would require implementing an activity logging system
    // For now, return empty result
    res.json({
      success: true,
      logs: [],
      pagination: {
        currentPage: parseInt(page),
        totalPages: 0,
        totalItems: 0,
        itemsPerPage: parseInt(limit)
      },
      message: 'Activity logging not yet implemented'
    });

  } catch (error) {
    console.error('Error getting user activity logs:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Manage two-factor authentication
exports.manageTwoFactor = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'UserTwoFactor');

    const { userId } = req.params;
    const targetUserId = userId || req.user._id;

    const user = await User.findById(targetUserId).select('username email isAuthApp');
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    res.json({
      success: true,
      twoFactorStatus: {
        enabled: user.isAuthApp,
        username: user.username,
        email: user.email
      }
    });

  } catch (error) {
    console.error('Error getting two-factor status:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Update two-factor authentication
exports.updateTwoFactor = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('update', 'UserTwoFactor');

    const { userId } = req.params;
    const targetUserId = userId || req.user._id;
    const { enabled } = req.body;

    const user = await User.findByIdAndUpdate(
      targetUserId,
      { isAuthApp: enabled },
      { new: true }
    ).select('username email isAuthApp');

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    res.json({
      success: true,
      message: `Two-factor authentication ${enabled ? 'enabled' : 'disabled'} successfully`,
      twoFactorStatus: {
        enabled: user.isAuthApp,
        username: user.username,
        email: user.email
      }
    });

  } catch (error) {
    console.error('Error updating two-factor authentication:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};
