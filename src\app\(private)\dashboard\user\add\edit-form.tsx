"use client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader } from "react-feather";
import { useForm } from "react-hook-form";
import {
  AdminEditResBody,
  AdminEditResBodyType,
  PasswordResBodyType,
  PasswordResBody,
} from "@/schemaValidations/user.schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
type UserFormValues = z.infer<typeof AdminEditResBody>;

type AddFormProps = {
  user?: UserFormValues;
  onSubmit: (data: UserFormValues) => Promise<void>; // Submit handler
  onSubmitPass: (data: PasswordResBodyType) => Promise<void>;
};

const EditForm = ({
  user,
  onSubmit,
  onSubmitPass,
}: AddFormProps) => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const genders = ["Male", "Female", "Not"];
  const ranks = ["1", "2", "3", "4", "5"];
  const rules = ["user", "manager", "editor"];

  const form = useForm<AdminEditResBodyType>({
    resolver: zodResolver(AdminEditResBody),
    defaultValues: user || {
      _id: "",
      email: "",
      username: "",
      phonenumber: "",
      private: false,
      rule: "user",
      rank: "1",
      gender: "Not",
      bio: "",
      permissions: [],
    },
  });
  const rule = form.watch("rule");

  // Reset form when user data changes
  React.useEffect(() => {
    if (user) {
      console.log("Resetting form with user data:", user);
      form.reset(user);
    }
  }, [user, form]);

  // Debug form state
  React.useEffect(() => {
    console.log("Form state:", {
      isValid: form.formState.isValid,
      errors: form.formState.errors,
      values: form.getValues()
    });

    // Log detailed errors
    if (Object.keys(form.formState.errors).length > 0) {
      console.log("Detailed validation errors:");
      Object.entries(form.formState.errors).forEach(([field, error]) => {
        console.log(`Field "${field}":`, error);
      });
    }
  }, [form.formState.isValid, form.formState.errors]);

  const formPass = useForm<PasswordResBodyType>({
    resolver: zodResolver(PasswordResBody),
    defaultValues: {
      _id: user?._id || "",
      password: "",
      confirmPassword: "",
    },
  });

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) => {
            console.log("Form submitted with data:", data);
            console.log("Form errors:", form.formState.errors);

            // Transform data to ensure correct types
            const transformedData = {
              ...data,
              phonenumber: data.phonenumber?.toString() || "",
              bio: data.bio || "",
              permissions: Array.isArray(data.permissions) ? data.permissions : []
            };

            console.log("Transformed data:", transformedData);
            onSubmit(transformedData);
          }, (errors) => {
            console.log("Form validation errors:", errors);
          })}
          className="px-12 flex-shrink-0 w-full"
          noValidate
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-4 relative">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>User Name</FormLabel>
                  <FormControl>
                    <Input placeholder="username" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="email" type="email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phonenumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Số điện thoại</FormLabel>
                  <FormControl>
                    <Input placeholder="Số điện thoại" type="text" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="gender"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Giới Tính</FormLabel>
                  <FormControl>
                    <select
                      {...field}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Chọn giới tính</option>
                      {genders.map((gender) => (
                        <option key={gender} value={gender}>
                          {gender}
                        </option>
                      ))}
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="rule"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Chức Vụ</FormLabel>
                  <FormControl>
                    <select
                      {...field}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      disabled={rule === "admin"} // Disable select when rule is "admin"
                    >
                      <option value="">Chọn chức vụ</option>
                      {rules.map((rank) => (
                        <option key={rank} value={rank}>
                          {rank}
                        </option>
                      ))}
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="rank"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cấp độ thành viên</FormLabel>
                  <FormControl>
                    <select
                      {...field}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Chọn cấp độ</option>
                      {ranks.map((rank) => (
                        <option key={rank} value={rank}>
                          {rank}
                        </option>
                      ))}
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tiểu sử</FormLabel>
                  <FormControl>
                    <textarea
                      {...field}
                      rows={3}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="private"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Khoá Thành viên</FormLabel>
                  <FormControl>
                    <div className="flex flex-col space-y-3 mt-2">
                      <label className="flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                        <input
                          type="radio"
                          name={`private-${field.name}`}
                          className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 focus:ring-2 mr-3"
                          value="true"
                          checked={field.value === true}
                          onChange={() => field.onChange(true)}
                        />
                        <div className="flex flex-col">
                          <span className="font-medium text-gray-900">Khoá Khách hàng</span>
                          <span className="text-sm text-gray-500">Tài khoản sẽ bị vô hiệu hóa</span>
                        </div>
                      </label>
                      <label className="flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                        <input
                          type="radio"
                          name={`private-${field.name}`}
                          className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 focus:ring-green-500 focus:ring-2 mr-3"
                          value="false"
                          checked={field.value === false}
                          onChange={() => field.onChange(false)}
                        />
                        <div className="flex flex-col">
                          <span className="font-medium text-gray-900">Hoạt động</span>
                          <span className="text-sm text-gray-500">Tài khoản hoạt động bình thường</span>
                        </div>
                      </label>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="permissions"
              render={({ field }) => {
                const permissionGroups = [
                  {
                    title: 'Thành Viên',
                    permissions: [
                      { id: 'user_view', name: 'Quản Lý Thành Viên', description: 'Xem danh sách và thông tin thành viên' },
                      { id: 'user_add', name: 'Thêm Thành Viên', description: 'Tạo tài khoản thành viên mới' },
                      { id: 'user_edit', name: 'Chỉnh Sửa Thành Viên', description: 'Cập nhật thông tin thành viên' },
                      { id: 'user_delete', name: 'Xóa Thành Viên', description: 'Xóa tài khoản thành viên' },
                      { id: 'user_import_csv', name: 'Nhập File CSV', description: 'Import thành viên từ file CSV' },
                    ]
                  },
                  {
                    title: 'Quản Lý File',
                    permissions: [
                      { id: 'file_view', name: 'Xem File', description: 'Xem danh sách file và tài liệu' },
                      { id: 'file_upload', name: 'Upload File', description: 'Tải lên file và tài liệu mới' },
                      { id: 'file_delete', name: 'Xóa File', description: 'Xóa file và tài liệu' },
                    ]
                  },
                  {
                    title: 'Quản Lý Vụ Việc Tòa Án',
                    permissions: [
                      { id: 'court_case_view', name: 'Xem Danh Sách Vụ Việc', description: 'Xem danh sách và thông tin vụ việc tòa án' },
                      { id: 'court_case_create', name: 'Tạo Vụ Việc Mới', description: 'Tạo vụ việc tòa án mới' },
                      { id: 'court_case_edit', name: 'Chỉnh Sửa Vụ Việc', description: 'Chỉnh sửa thông tin vụ việc tòa án' },
                      { id: 'court_case_delete', name: 'Xóa Vụ Việc', description: 'Xóa vụ việc tòa án' },
                      { id: 'court_case_export', name: 'Xuất Dữ Liệu Vụ Việc', description: 'Xuất danh sách vụ việc ra file Excel/CSV' },
                      { id: 'court_case_import', name: 'Nhập Dữ Liệu Vụ Việc', description: 'Nhập danh sách vụ việc từ file Excel' },
                      { id: 'court_case_stats_view', name: 'Xem Thống Kê Vụ Việc', description: 'Xem thống kê cơ bản về vụ việc tòa án' },
                      { id: 'court_case_detailed_stats_view', name: 'Xem Thống Kê Chi Tiết', description: 'Xem thống kê chi tiết và báo cáo phân tích' },
                    ]
                  },
                  {
                    title: 'Quản Lý Tài Khoản Trong Vụ Việc',
                    permissions: [
                      { id: 'court_case_user_profile_view', name: 'Xem Hồ Sơ Người Dùng', description: 'Xem thông tin hồ sơ người dùng trong hệ thống vụ việc' },
                      { id: 'court_case_user_profile_edit', name: 'Chỉnh Sửa Hồ Sơ', description: 'Chỉnh sửa thông tin hồ sơ người dùng' },
                      { id: 'court_case_user_password_change', name: 'Đổi Mật Khẩu Người Dùng', description: 'Thay đổi mật khẩu cho người dùng' },
                      { id: 'court_case_user_permissions_view', name: 'Xem Quyền Hạn', description: 'Xem danh sách quyền hạn của người dùng' },
                      { id: 'court_case_user_permissions_edit', name: 'Chỉnh Sửa Quyền Hạn', description: 'Cấp và thu hồi quyền hạn cho người dùng' },
                      { id: 'court_case_user_activity_log_view', name: 'Xem Nhật Ký Hoạt Động', description: 'Xem lịch sử hoạt động của người dùng' },
                      { id: 'court_case_user_two_factor_manage', name: 'Quản Lý Xác Thực 2 Yếu Tố', description: 'Bật/tắt và quản lý xác thực 2 yếu tố' },
                    ]
                  },
                  {
                    title: 'Cài Đặt Hệ Thống',
                    permissions: [
                      { id: 'system_settings_view', name: 'Xem Cài Đặt', description: 'Xem cấu hình hệ thống' },
                      { id: 'system_settings_edit', name: 'Chỉnh Sửa Cài Đặt', description: 'Thay đổi cấu hình hệ thống' },
                    ]
                  },
                  {
                    title: 'Thống Kê & Phân Quyền',
                    permissions: [
                      { id: 'analytics_view', name: 'Xem Thống Kê', description: 'Truy cập báo cáo và thống kê' },
                      { id: 'permissions_manage', name: 'Quản Lý Phân Quyền', description: 'Cấp và thu hồi quyền cho người dùng' },
                    ]
                  }
                ];

                return (
                  <FormItem className="col-span-2">
                    <FormLabel>Phân quyền chức năng chi tiết</FormLabel>
                    <div className="space-y-6 mt-4">
                      {permissionGroups.map((group) => (
                        <div key={group.title} className="border border-gray-200 rounded-lg p-4">
                          <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            {group.title}
                          </h4>
                          <div className="space-y-2">
                            {group.permissions.map((permission) => (
                              <label key={permission.id} className="flex items-start space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors">
                                <input
                                  type="checkbox"
                                  className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                  value={permission.id}
                                  checked={field.value?.includes(permission.id)}
                                  onChange={(e) => {
                                    const newValue = e.target.checked
                                      ? [...(field.value || []), permission.id]
                                      : field.value?.filter((id: string) => id !== permission.id) || [];
                                    field.onChange(newValue);
                                  }}
                                />
                                <div className="flex-1">
                                  <div className="font-medium text-gray-900">{permission.name}</div>
                                  <div className="text-sm text-gray-500">{permission.description}</div>
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="text-xs text-gray-500 mt-3 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                      <strong>Lưu ý:</strong> Admin luôn có tất cả quyền. Chỉ cần cấp quyền cụ thể cho Manager và User.
                    </div>
                  </FormItem>
                );
              }}
            />
          </div>
          <div className="mt-2 text-red-500 text-sm font-medium">
            {errorMessage}
          </div>
          <div className="flex gap-4 justify-center mt-6">
            <button
              disabled={loading ? true : false}
              type="submit"
              onClick={() => {
                console.log("Submit button clicked");
                console.log("Loading state:", loading);
                console.log("Form valid:", form.formState.isValid);
                console.log("Form errors:", form.formState.errors);
              }}
              className="btn btn-primary bg-blue-700 w-40 text-white flex items-center"
            >
              {loading ? <Loader className="animate-spin" /> : ""}
              Xác Nhận
            </button>
          </div>
        </form>
      </Form>
      <Form {...formPass}>
        <form
          onSubmit={formPass.handleSubmit(onSubmitPass)}
          className="space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto mt-8"
          noValidate
        >
          <FormField
            control={formPass.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mật khẩu mới</FormLabel>
                <FormControl>
                  <Input placeholder="password" type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={formPass.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Xác nhận mật khẩu</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Xác nhận mật khẩu"
                    type="password"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <button
            disabled={loading ? true : false}
            type="submit"
            className="btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center"
          >
            {loading ? <Loader className="animate-spin" /> : ""}
            Update Password
          </button>
        </form>
      </Form>
    </>
  );
};

export default EditForm;
