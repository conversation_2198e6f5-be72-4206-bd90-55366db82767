const express = require('express');
const router = express.Router();
const courtCaseUserController = require('../controllers/courtCaseUserManagement');
const passport = require('passport');

// Apply authentication middleware to all routes
router.use(passport.authenticate('user', { session: false }));

// User Profile Management Routes
// GET /api/court-cases/user-management/profile/:userId? - Get user profile
router.get('/profile/:userId?', courtCaseUserController.getUserProfile);

// PUT /api/court-cases/user-management/profile/:userId? - Update user profile
router.put('/profile/:userId?', courtCaseUserController.updateUserProfile);

// POST /api/court-cases/user-management/change-password/:userId? - Change user password
router.post('/change-password/:userId?', courtCaseUserController.changeUserPassword);

// User Permissions Management Routes
// GET /api/court-cases/user-management/permissions/:userId - Get user permissions
router.get('/permissions/:userId', courtCaseUserController.getUserPermissions);

// PUT /api/court-cases/user-management/permissions/:userId - Update user permissions
router.put('/permissions/:userId', courtCaseUserController.updateUserPermissions);

// User Activity Logs Routes
// GET /api/court-cases/user-management/activity-logs/:userId - Get user activity logs
router.get('/activity-logs/:userId', courtCaseUserController.getUserActivityLogs);

// Two-Factor Authentication Routes
// GET /api/court-cases/user-management/two-factor/:userId? - Get two-factor status
router.get('/two-factor/:userId?', courtCaseUserController.manageTwoFactor);

// PUT /api/court-cases/user-management/two-factor/:userId? - Update two-factor settings
router.put('/two-factor/:userId?', courtCaseUserController.updateTwoFactor);

module.exports = router;
