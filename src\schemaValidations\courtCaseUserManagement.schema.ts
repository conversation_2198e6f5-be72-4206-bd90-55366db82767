import { z } from 'zod';

// User profile schema for court case management
export const CourtCaseUserProfileSchema = z.object({
  _id: z.string(),
  username: z.string(),
  email: z.string().email(),
  phonenumber: z.string().optional(),
  rule: z.enum(['admin', 'manager', 'editor', 'user']),
  permissions: z.array(z.string()),
  avatar: z.string().optional(),
  bio: z.string().optional(),
  gender: z.enum(['Male', 'Female', 'Not']).optional(),
  rank: z.enum(['1', '2', '3', '4', '5']).optional(),
  point: z.number().optional(),
  isMail: z.boolean().optional(),
  isAuthApp: z.boolean().optional(),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const CourtCaseUserProfileUpdateSchema = z.object({
  username: z.string().min(1).optional(),
  email: z.string().email().optional(),
  phonenumber: z.string().optional(),
  bio: z.string().optional(),
  gender: z.enum(['Male', 'Female', 'Not']).optional(),
  avatar: z.string().optional()
});

export const CourtCaseUserPasswordChangeSchema = z.object({
  currentPassword: z.string().optional(),
  newPassword: z.string().min(6, 'Password must be at least 6 characters long'),
  confirmPassword: z.string().min(6, 'Password must be at least 6 characters long')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const CourtCaseUserPermissionsSchema = z.object({
  _id: z.string(),
  username: z.string(),
  email: z.string().email(),
  rule: z.enum(['admin', 'manager', 'editor', 'user']),
  permissions: z.array(z.string())
});

export const CourtCaseUserPermissionsUpdateSchema = z.object({
  rule: z.enum(['admin', 'manager', 'editor', 'user']).optional(),
  permissions: z.array(z.string()).optional()
});

export const CourtCaseUserActivityLogSchema = z.object({
  _id: z.string(),
  userId: z.string(),
  action: z.string(),
  resource: z.string().optional(),
  resourceId: z.string().optional(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  timestamp: z.string()
});

export const CourtCaseUserTwoFactorSchema = z.object({
  enabled: z.boolean(),
  username: z.string(),
  email: z.string()
});

export const CourtCaseUserTwoFactorUpdateSchema = z.object({
  enabled: z.boolean()
});

// Response types
export const CourtCaseUserProfileResponseSchema = z.object({
  success: z.boolean(),
  user: CourtCaseUserProfileSchema
});

export const CourtCaseUserPermissionsResponseSchema = z.object({
  success: z.boolean(),
  user: CourtCaseUserPermissionsSchema
});

export const CourtCaseUserActivityLogsResponseSchema = z.object({
  success: z.boolean(),
  logs: z.array(CourtCaseUserActivityLogSchema),
  pagination: z.object({
    currentPage: z.number(),
    totalPages: z.number(),
    totalItems: z.number(),
    itemsPerPage: z.number()
  }),
  message: z.string().optional()
});

export const CourtCaseUserTwoFactorResponseSchema = z.object({
  success: z.boolean(),
  twoFactorStatus: CourtCaseUserTwoFactorSchema
});

export const CourtCaseUserGenericResponseSchema = z.object({
  success: z.boolean(),
  message: z.string()
});

// Type exports
export type CourtCaseUserProfileType = z.infer<typeof CourtCaseUserProfileSchema>;
export type CourtCaseUserProfileUpdateType = z.infer<typeof CourtCaseUserProfileUpdateSchema>;
export type CourtCaseUserPasswordChangeType = z.infer<typeof CourtCaseUserPasswordChangeSchema>;
export type CourtCaseUserPermissionsType = z.infer<typeof CourtCaseUserPermissionsSchema>;
export type CourtCaseUserPermissionsUpdateType = z.infer<typeof CourtCaseUserPermissionsUpdateSchema>;
export type CourtCaseUserActivityLogType = z.infer<typeof CourtCaseUserActivityLogSchema>;
export type CourtCaseUserTwoFactorType = z.infer<typeof CourtCaseUserTwoFactorSchema>;
export type CourtCaseUserTwoFactorUpdateType = z.infer<typeof CourtCaseUserTwoFactorUpdateSchema>;

export type CourtCaseUserProfileResponseType = z.infer<typeof CourtCaseUserProfileResponseSchema>;
export type CourtCaseUserPermissionsResponseType = z.infer<typeof CourtCaseUserPermissionsResponseSchema>;
export type CourtCaseUserActivityLogsResponseType = z.infer<typeof CourtCaseUserActivityLogsResponseSchema>;
export type CourtCaseUserTwoFactorResponseType = z.infer<typeof CourtCaseUserTwoFactorResponseSchema>;
export type CourtCaseUserGenericResponseType = z.infer<typeof CourtCaseUserGenericResponseSchema>;

// Available permissions list for court case management
export const COURT_CASE_PERMISSIONS = [
  // User Management
  'user_view',
  'user_add', 
  'user_edit',
  'user_delete',
  'user_import_csv',

  // File Management
  'file_view',
  'file_upload',
  'file_delete',

  // Court Case Management
  'court_case_view',
  'court_case_create',
  'court_case_edit',
  'court_case_delete',
  'court_case_export',
  'court_case_import',
  'court_case_stats_view',
  'court_case_detailed_stats_view',
  
  // Court Case User Account Management
  'court_case_user_profile_view',
  'court_case_user_profile_edit',
  'court_case_user_password_change',
  'court_case_user_permissions_view',
  'court_case_user_permissions_edit',
  'court_case_user_activity_log_view',
  'court_case_user_two_factor_manage',

  // System Settings
  'system_settings_view',
  'system_settings_edit',

  // Analytics
  'analytics_view',

  // Permission Management
  'permissions_manage'
] as const;

export type CourtCasePermissionType = typeof COURT_CASE_PERMISSIONS[number];
