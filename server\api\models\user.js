const mongoose = require("mongoose");

const { Schema } = mongoose;
const bcrypt = require("bcryptjs");
const { accessibleRecordsPlugin } = require("@casl/mongoose");

const userSchema = new Schema(
  {
    username: { type: String, required: false },
    phonenumber: { type: String, unique: true, sparse: true, required: false  },
    email: {
      type: String,
      required: true,
      unique: true,
      match:
        // eslint-disable-next-line no-useless-escape
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    },
    password: {
      type: String,
      required: true,
    },
    resetlink: {
      type: String,
      default: "",
    },
    private: {
      type: Boolean,
      default: false,
    },
    isMail: {
      type: Boolean,
      default: false,
    },
    isAuthApp: {
      type: Boolean,
      default: false,
    },
    twoFactorSecret: String,
    rule: { type: String, enum: ["admin", "manager", "editor", "user"], default: "user" },
    permissions: [
      {
        type: String,
        enum: [
          // User Management
          "user_view",
          "user_add",
          "user_edit",
          "user_delete",
          "user_import_csv",

          // File Management
          "file_view",
          "file_upload",
          "file_delete",

          // Court Case Management
          "court_case_view",
          "court_case_create",
          "court_case_edit",
          "court_case_delete",
          "court_case_export",
          "court_case_import",
          "court_case_stats_view",
          "court_case_detailed_stats_view",
          
          // Court Case User Account Management
          "court_case_user_profile_view",
          "court_case_user_profile_edit",
          "court_case_user_password_change",
          "court_case_user_permissions_view",
          "court_case_user_permissions_edit",
          "court_case_user_activity_log_view",
          "court_case_user_two_factor_manage",

          // System Settings
          "system_settings_view",
          "system_settings_edit",

          // Analytics
          "analytics_view",

          // Permission Management
          "permissions_manage"
        ]
      },
    ],
    point: { type: Number, default: 0 },
    code: { type: String, index: true, unique: true },
    Passcode: { type: Schema.Types.ObjectId, ref: "Passcode" },
    avatar: { type: String },
    address: { type: Schema.Types.ObjectId, ref: "Address" },
    rank: { type: String, enum: ["1", "2", "3", "4", "5"], default: "1" },
    bio: { type: String },
    gender: { type: String, enum: ["Male", "Female", "Not"], default: "Not" },
  },
  { timestamps: true }
);

userSchema.plugin(accessibleRecordsPlugin);

userSchema.methods.comparePassword = function (password) {
  const user = this;
  return bcrypt.compareSync(password, user.password);
};

userSchema.index({ phonenumber: 1 }, { unique: true, partialFilterExpression: { phonenumber: { $exists: true } } });

module.exports = mongoose.model("User", userSchema);
