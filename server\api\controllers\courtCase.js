const CourtCase = require('../../models/CourtCase');
const { defineAbilityFor } = require('../permissions/abilities');
const { ForbiddenError } = require('@casl/ability');

// Get all court cases with pagination, filtering, and sorting
exports.getCourtCases = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCase');

    const {
      page = 1,
      limit = 20,
      search = '',
      loaiAn = '',
      trangThaiGiaiQuyet = '',
      thuTucApDung = '',
      fromDate = '',
      toDate = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (search) {
      filter.$or = [
        { soThuLy: { $regex: search, $options: 'i' } },
        { soBanAn: { $regex: search, $options: 'i' } },
        { biCaoNguoiKhieuKien: { $regex: search, $options: 'i' } },
        { toiDanhNoiDung: { $regex: search, $options: 'i' } },
        { thamPhanPhuTrach: { $regex: search, $options: 'i' } }
      ];
    }

    if (loaiAn) filter.loaiAn = loaiAn;
    if (trangThaiGiaiQuyet) filter.trangThaiGiaiQuyet = trangThaiGiaiQuyet;
    if (thuTucApDung) filter.thuTucApDung = thuTucApDung;

    // Date range filter
    if (fromDate || toDate) {
      filter.ngayThuLy = {};
      if (fromDate) filter.ngayThuLy.$gte = new Date(fromDate);
      if (toDate) filter.ngayThuLy.$lte = new Date(toDate);
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const [cases, total] = await Promise.all([
      CourtCase.find(filter)
        .populate('createdBy', 'username email')
        .populate('updatedBy', 'username email')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      CourtCase.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      cases,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Error getting court cases:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get single court case by ID
exports.getCourtCaseById = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { id } = req.params;
    
    const courtCase = await CourtCase.findById(id)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .lean();

    if (!courtCase) {
      return res.status(404).json({ success: false, message: 'Court case not found' });
    }

    ForbiddenError.from(ability).throwUnlessCan('read', courtCase);

    res.json({
      success: true,
      case: courtCase
    });

  } catch (error) {
    console.error('Error getting court case:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Create new court case
exports.createCourtCase = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('create', 'CourtCase');

    // Get next STT number only if not provided
    let sttNumber = req.body.stt;
    if (!sttNumber) {
      const lastCase = await CourtCase.findOne({ stt: { $exists: true, $ne: null } }).sort({ stt: -1 }).lean();
      sttNumber = lastCase ? lastCase.stt + 1 : 1;
    }

    // Clean up empty strings and convert to null for dates
    const cleanedData = { ...req.body };

    // Convert empty strings to null for date fields
    if (cleanedData.ngayThuLy === '') cleanedData.ngayThuLy = null;
    if (cleanedData.ngayBanHanh === '') cleanedData.ngayBanHanh = null;

    // Convert empty strings to empty string for text fields (keep consistent)
    Object.keys(cleanedData).forEach(key => {
      if (cleanedData[key] === undefined || cleanedData[key] === null) {
        cleanedData[key] = '';
      }
    });

    const courtCaseData = {
      ...cleanedData,
      stt: sttNumber,
      createdBy: req.user ? req.user.id : null
    };

    const courtCase = new CourtCase(courtCaseData);
    await courtCase.save();

    const populatedCase = await CourtCase.findById(courtCase._id)
      .populate('createdBy', 'username email')
      .lean();

    res.status(201).json({
      success: true,
      message: 'Court case created successfully',
      case: populatedCase
    });

  } catch (error) {
    console.error('Error creating court case:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else if (error.code === 11000) {
      // Handle duplicate key error
      const duplicateField = Object.keys(error.keyPattern)[0];
      const message = duplicateField === 'stt' ? 'STT already exists' :
                     duplicateField === 'soThuLy' ? 'Số thụ lý already exists' :
                     'Duplicate entry exists';
      res.status(400).json({ success: false, message });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error', details: error.message });
    }
  }
};

// Update court case
exports.updateCourtCase = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id } = req.params;

    const existingCase = await CourtCase.findById(id);
    if (!existingCase) {
      return res.status(404).json({ success: false, message: 'Court case not found' });
    }

    ForbiddenError.from(ability).throwUnlessCan('update', existingCase);

    // Clean up empty strings and convert to null for dates
    const cleanedData = { ...req.body };

    // Convert empty strings to null for date fields
    if (cleanedData.ngayThuLy === '') cleanedData.ngayThuLy = null;
    if (cleanedData.ngayBanHanh === '') cleanedData.ngayBanHanh = null;

    // Convert empty strings to empty string for text fields (keep consistent)
    Object.keys(cleanedData).forEach(key => {
      if (cleanedData[key] === undefined || cleanedData[key] === null) {
        cleanedData[key] = '';
      }
    });

    const updateData = {
      ...cleanedData,
      updatedBy: req.user ? req.user.id : null
    };

    const courtCase = await CourtCase.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('createdBy', 'username email')
     .populate('updatedBy', 'username email')
     .lean();

    res.json({
      success: true,
      message: 'Court case updated successfully',
      case: courtCase
    });

  } catch (error) {
    console.error('Error updating court case:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else if (error.code === 11000) {
      // Handle duplicate key error
      const duplicateField = Object.keys(error.keyPattern)[0];
      const message = duplicateField === 'stt' ? 'STT already exists' :
                     duplicateField === 'soThuLy' ? 'Số thụ lý already exists' :
                     'Duplicate entry exists';
      res.status(400).json({ success: false, message });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error', details: error.message });
    }
  }
};

// Delete court case
exports.deleteCourtCase = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { id } = req.params;
    
    const courtCase = await CourtCase.findById(id);
    if (!courtCase) {
      return res.status(404).json({ success: false, message: 'Court case not found' });
    }

    ForbiddenError.from(ability).throwUnlessCan('delete', courtCase);

    await CourtCase.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Court case deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting court case:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get court case statistics
exports.getCourtCaseStats = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCase');
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCaseStats');

    const stats = await CourtCase.aggregate([
      {
        $group: {
          _id: '$trangThaiGiaiQuyet',
          count: { $sum: 1 }
        }
      }
    ]);

    const totalCases = await CourtCase.countDocuments();

    const casesByType = await CourtCase.aggregate([
      {
        $group: {
          _id: '$loaiAn',
          count: { $sum: 1 }
        }
      }
    ]);

    const casesByProcedure = await CourtCase.aggregate([
      {
        $group: {
          _id: '$thuTucApDung',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      stats: {
        total: totalCases,
        byStatus: stats,
        byType: casesByType,
        byProcedure: casesByProcedure
      }
    });

  } catch (error) {
    console.error('Error getting court case stats:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Export court cases to Excel
exports.exportCourtCases = async (req, res) => {
  console.log('[DEBUG] Export request - User:', req.user ? {
    id: req.user._id,
    email: req.user.email,
    rule: req.user.rule,
    permissions: req.user.permissions
  } : 'No user');

  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCase');
    ForbiddenError.from(ability).throwUnlessCan('export', 'CourtCase');

    const {
      search = '',
      loaiAn = '',
      trangThaiGiaiQuyet = '',
      thuTucApDung = '',
      fromDate = '',
      toDate = ''
    } = req.query;

    // Build filter object (same as getCourtCases)
    const filter = {};

    if (search) {
      filter.$or = [
        { soThuLy: { $regex: search, $options: 'i' } },
        { soBanAn: { $regex: search, $options: 'i' } },
        { biCaoNguoiKhieuKien: { $regex: search, $options: 'i' } },
        { toiDanhNoiDung: { $regex: search, $options: 'i' } },
        { thamPhanPhuTrach: { $regex: search, $options: 'i' } }
      ];
    }

    if (loaiAn) filter.loaiAn = loaiAn;
    if (trangThaiGiaiQuyet) filter.trangThaiGiaiQuyet = trangThaiGiaiQuyet;
    if (thuTucApDung) filter.thuTucApDung = thuTucApDung;

    // Date range filter
    if (fromDate || toDate) {
      filter.ngayThuLy = {};
      if (fromDate) filter.ngayThuLy.$gte = new Date(fromDate);
      if (toDate) filter.ngayThuLy.$lte = new Date(toDate);
    }

    // Get all matching cases
    const cases = await CourtCase.find(filter)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .sort({ stt: 1 })
      .lean();

    // Create Excel workbook
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Danh sách vụ việc');

    // Define columns
    worksheet.columns = [
      { header: 'STT', key: 'stt', width: 10 },
      { header: 'Loại án', key: 'loaiAn', width: 15 },
      { header: 'Số thụ lý', key: 'soThuLy', width: 20 },
      { header: 'Ngày thụ lý', key: 'ngayThuLy', width: 15 },
      { header: 'TAND', key: 'tand', width: 30 },
      { header: 'Số bản án/QĐ', key: 'soBanAn', width: 20 },
      { header: 'Ngày ban hành', key: 'ngayBanHanh', width: 15 },
      { header: 'Bị cáo/Người khiếu kiện', key: 'biCaoNguoiKhieuKien', width: 40 },
      { header: 'Tội danh/Nội dung', key: 'toiDanhNoiDung', width: 40 },
      { header: 'Quan hệ pháp luật', key: 'quanHePhatLuat', width: 30 },
      { header: 'Hình thức xử lý', key: 'hinhThucXuLy', width: 25 },
      { header: 'Thủ tục áp dụng', key: 'thuTucApDung', width: 20 },
      { header: 'Thẩm phán phụ trách', key: 'thamPhanPhuTrach', width: 25 },
      { header: 'Trưởng/Phó phòng KTNV', key: 'truongPhoPhongKTNV', width: 30 },
      { header: 'Trạng thái', key: 'trangThaiGiaiQuyet', width: 20 },
      { header: 'Ghi chú', key: 'ghiChu', width: 30 },
      { header: 'Ghi chú kết quả', key: 'ghiChuKetQua', width: 30 },
      { header: 'Người tạo', key: 'createdBy', width: 20 },
      { header: 'Ngày tạo', key: 'createdAt', width: 15 }
    ];

    // Style header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    };

    // Add data rows
    cases.forEach(courtCase => {
      worksheet.addRow({
        stt: courtCase.stt,
        loaiAn: courtCase.loaiAn,
        soThuLy: courtCase.soThuLy,
        ngayThuLy: courtCase.ngayThuLy ? new Date(courtCase.ngayThuLy).toLocaleDateString('vi-VN') : '',
        tand: courtCase.tand,
        soBanAn: courtCase.soBanAn,
        ngayBanHanh: courtCase.ngayBanHanh ? new Date(courtCase.ngayBanHanh).toLocaleDateString('vi-VN') : '',
        biCaoNguoiKhieuKien: courtCase.biCaoNguoiKhieuKien,
        toiDanhNoiDung: courtCase.toiDanhNoiDung,
        quanHePhatLuat: courtCase.quanHePhatLuat,
        hinhThucXuLy: courtCase.hinhThucXuLy,
        thuTucApDung: courtCase.thuTucApDung,
        thamPhanPhuTrach: courtCase.thamPhanPhuTrach,
        truongPhoPhongKTNV: courtCase.truongPhoPhongKTNV,
        trangThaiGiaiQuyet: courtCase.trangThaiGiaiQuyet,
        ghiChu: courtCase.ghiChu || '',
        ghiChuKetQua: courtCase.ghiChuKetQua || '',
        createdBy: courtCase.createdBy?.username || '',
        createdAt: courtCase.createdAt ? new Date(courtCase.createdAt).toLocaleDateString('vi-VN') : ''
      });
    });

    // Set response headers
    const fileName = `danh-sach-vu-viec-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

    // Write to response
    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('Error exporting court cases:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Import court cases from Excel
exports.importCourtCases = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('create', 'CourtCase');
    ForbiddenError.from(ability).throwUnlessCan('import', 'CourtCase');

    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(req.file.buffer);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      return res.status(400).json({ success: false, message: 'No worksheet found in Excel file' });
    }

    const importResults = {
      total: 0,
      success: 0,
      errors: [],
      duplicates: 0
    };

    // Skip header row, start from row 2
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // Skip empty rows
      if (!row.hasValues) continue;

      importResults.total++;

      try {
        // Map Excel columns to database fields
        const courtCaseData = {
          stt: row.getCell(1).value || undefined,
          loaiAn: row.getCell(2).value || '',
          soThuLy: row.getCell(3).value || '',
          ngayThuLy: row.getCell(4).value ? new Date(row.getCell(4).value) : null,
          tand: row.getCell(5).value || '',
          soBanAn: row.getCell(6).value || '',
          ngayBanHanh: row.getCell(7).value ? new Date(row.getCell(7).value) : null,
          biCaoNguoiKhieuKien: row.getCell(8).value || '',
          toiDanhNoiDung: row.getCell(9).value || '',
          quanHePhatLuat: row.getCell(10).value || '',
          hinhThucXuLy: row.getCell(11).value || '',
          thuTucApDung: row.getCell(12).value || '',
          thamPhanPhuTrach: row.getCell(13).value || '',
          truongPhoPhongKTNV: row.getCell(14).value || '',
          trangThaiGiaiQuyet: row.getCell(15).value || 'Chưa giải quyết',
          ghiChu: row.getCell(16).value || '',
          ghiChuKetQua: row.getCell(17).value || '',
          createdBy: req.user ? req.user.id : null
        };

        // Auto-generate STT if not provided
        if (!courtCaseData.stt) {
          const lastCase = await CourtCase.findOne({ stt: { $exists: true, $ne: null } }).sort({ stt: -1 }).lean();
          courtCaseData.stt = lastCase ? lastCase.stt + 1 : importResults.success + 1;
        }

        const courtCase = new CourtCase(courtCaseData);
        await courtCase.save();
        importResults.success++;

      } catch (error) {
        if (error.code === 11000) {
          importResults.duplicates++;
          importResults.errors.push(`Row ${rowNumber}: Duplicate entry (STT or Số thụ lý already exists)`);
        } else {
          importResults.errors.push(`Row ${rowNumber}: ${error.message}`);
        }
      }
    }

    res.json({
      success: true,
      message: 'Import completed',
      results: importResults
    });

  } catch (error) {
    console.error('Error importing court cases:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error', details: error.message });
    }
  }
};

// Get detailed statistics
exports.getDetailedStats = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCase');
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCaseDetailedStats');

    const { fromDate, toDate, groupBy = 'month' } = req.query;

    // Build date filter
    const dateFilter = {};
    if (fromDate || toDate) {
      dateFilter.ngayThuLy = {};
      if (fromDate) dateFilter.ngayThuLy.$gte = new Date(fromDate);
      if (toDate) dateFilter.ngayThuLy.$lte = new Date(toDate);
    }

    // Basic stats
    const totalCases = await CourtCase.countDocuments(dateFilter);

    // Status distribution
    const statusStats = await CourtCase.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$trangThaiGiaiQuyet',
          count: { $sum: 1 }
        }
      }
    ]);

    // Case type distribution
    const typeStats = await CourtCase.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$loaiAn',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Procedure distribution
    const procedureStats = await CourtCase.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$thuTucApDung',
          count: { $sum: 1 }
        }
      }
    ]);

    // Processing method distribution
    const processingStats = await CourtCase.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$hinhThucXuLy',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Time-based trends
    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$ngayThuLy" } };
        break;
      case 'week':
        groupFormat = { $dateToString: { format: "%Y-W%U", date: "$ngayThuLy" } };
        break;
      case 'quarter':
        groupFormat = {
          $concat: [
            { $toString: { $year: "$ngayThuLy" } },
            "-Q",
            { $toString: { $ceil: { $divide: [{ $month: "$ngayThuLy" }, 3] } } }
          ]
        };
        break;
      case 'year':
        groupFormat = { $toString: { $year: "$ngayThuLy" } };
        break;
      default: // month
        groupFormat = { $dateToString: { format: "%Y-%m", date: "$ngayThuLy" } };
    }

    const trends = await CourtCase.aggregate([
      { $match: { ...dateFilter, ngayThuLy: { $ne: null } } },
      {
        $group: {
          _id: groupFormat,
          count: { $sum: 1 },
          resolved: {
            $sum: {
              $cond: [{ $eq: ['$trangThaiGiaiQuyet', 'Đã giải quyết'] }, 1, 0]
            }
          },
          pending: {
            $sum: {
              $cond: [{ $eq: ['$trangThaiGiaiQuyet', 'Chưa giải quyết'] }, 1, 0]
            }
          },
          inProgress: {
            $sum: {
              $cond: [{ $eq: ['$trangThaiGiaiQuyet', 'Đang giải quyết'] }, 1, 0]
            }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Top judges by case count
    const topJudges = await CourtCase.aggregate([
      { $match: { ...dateFilter, thamPhanPhuTrach: { $ne: '' } } },
      {
        $group: {
          _id: '$thamPhanPhuTrach',
          count: { $sum: 1 },
          resolved: {
            $sum: {
              $cond: [{ $eq: ['$trangThaiGiaiQuyet', 'Đã giải quyết'] }, 1, 0]
            }
          }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Court distribution
    const courtStats = await CourtCase.aggregate([
      { $match: { ...dateFilter, tand: { $ne: '' } } },
      {
        $group: {
          _id: '$tand',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      stats: {
        summary: {
          total: totalCases,
          period: fromDate && toDate ? `${fromDate} to ${toDate}` : 'All time'
        },
        byStatus: statusStats,
        byType: typeStats,
        byProcedure: procedureStats,
        byProcessingMethod: processingStats,
        trends: trends,
        topJudges: topJudges,
        byCourt: courtStats
      }
    });

  } catch (error) {
    console.error('Error getting detailed stats:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};
