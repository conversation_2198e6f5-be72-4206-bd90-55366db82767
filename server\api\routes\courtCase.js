const express = require('express');
const router = express.Router();
const courtCaseController = require('../controllers/courtCase');
const passport = require('passport');

// Apply authentication middleware to all routes
router.use(passport.authenticate('user', { session: false }));

// GET /api/court-cases - Get all court cases with pagination and filtering
router.get('/', courtCaseController.getCourtCases);

// GET /api/court-cases/stats - Get court case statistics
router.get('/stats', courtCaseController.getCourtCaseStats);

// GET /api/court-cases/export - Export court cases to Excel
router.get('/export', courtCaseController.exportCourtCases);

// GET /api/court-cases/detailed-stats - Get detailed statistics
router.get('/detailed-stats', courtCaseController.getDetailedStats);

// POST /api/court-cases/import - Import court cases from Excel
const multer = require('multer');
const upload = multer({ storage: multer.memoryStorage() });
router.post('/import', upload.single('file'), courtCaseController.importCourtCases);

// GET /api/court-cases/:id - Get single court case by ID
router.get('/:id', courtCaseController.getCourtCaseById);

// POST /api/court-cases - Create new court case
router.post('/', courtCaseController.createCourtCase);

// PUT /api/court-cases/:id - Update court case
router.put('/:id', courtCaseController.updateCourtCase);

// DELETE /api/court-cases/:id - Delete court case
router.delete('/:id', courtCaseController.deleteCourtCase);

module.exports = router;
