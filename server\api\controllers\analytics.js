const { PageView, DailyAnalytics } = require('../models/analytics');
const Post = require('../models/post');
const { defineAbilityFor } = require('../permissions/abilities');
const { ForbiddenError } = require('@casl/ability');

// Get analytics overview
exports.getAnalyticsOverview = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  try {
    ForbiddenError.from(ability).throwUnlessCan("read", "dashboard");

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    
    const lastMonth = new Date(today);
    lastMonth.setDate(lastMonth.getDate() - 30);

    // Get today's analytics
    const todayAnalytics = await DailyAnalytics.findOne({ date: today });
    
    // Get yesterday's analytics for comparison
    const yesterdayAnalytics = await DailyAnalytics.findOne({ date: yesterday });

    // Get total views for different periods
    const [
      totalViewsAllTime,
      totalViewsThisMonth,
      totalViewsThisWeek,
      totalViewsToday,
      uniqueVisitorsToday,
      topPostsToday
    ] = await Promise.all([
      PageView.countDocuments(),
      PageView.countDocuments({ timestamp: { $gte: lastMonth } }),
      PageView.countDocuments({ timestamp: { $gte: lastWeek } }),
      PageView.countDocuments({ timestamp: { $gte: today } }),
      PageView.distinct('sessionId', { timestamp: { $gte: today } }).then(sessions => sessions.length),
      PageView.aggregate([
        { $match: { timestamp: { $gte: today }, page: 'post' } },
        { $group: { _id: '$postId', views: { $sum: 1 } } },
        { $sort: { views: -1 } },
        { $limit: 5 },
        { $lookup: { from: 'posts', localField: '_id', foreignField: '_id', as: 'post' } },
        { $unwind: '$post' },
        { $project: { views: 1, title: '$post.title', slug: '$post.slug' } }
      ])
    ]);

    // Calculate growth percentages
    const homeViewsGrowth = yesterdayAnalytics && yesterdayAnalytics.homeViews > 0 
      ? ((todayAnalytics?.homeViews || 0) - yesterdayAnalytics.homeViews) / yesterdayAnalytics.homeViews * 100
      : 0;

    const postViewsGrowth = yesterdayAnalytics && yesterdayAnalytics.postViews > 0
      ? ((todayAnalytics?.postViews || 0) - yesterdayAnalytics.postViews) / yesterdayAnalytics.postViews * 100
      : 0;

    res.status(200).json({
      success: true,
      analytics: {
        overview: {
          totalViewsAllTime,
          totalViewsThisMonth,
          totalViewsThisWeek,
          totalViewsToday,
          homeViewsToday: todayAnalytics?.homeViews || 0,
          postViewsToday: todayAnalytics?.postViews || 0,
          uniqueVisitorsToday,
          homeViewsGrowth: Math.round(homeViewsGrowth * 100) / 100,
          postViewsGrowth: Math.round(postViewsGrowth * 100) / 100
        },
        topPostsToday
      }
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cannot get analytics overview",
      error: err,
      success: false,
    });
  }
};

// Get analytics chart data
exports.getAnalyticsChartData = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  try {
    ForbiddenError.from(ability).throwUnlessCan("read", "dashboard");

    const days = parseInt(req.query.days) || 7;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    startDate.setHours(0, 0, 0, 0);

    // Get daily analytics for the period
    const dailyData = await DailyAnalytics.find({
      date: { $gte: startDate }
    }).sort({ date: 1 });

    // Fill missing days with zero data
    const chartData = [];
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      const dayData = dailyData.find(d => 
        d.date.toDateString() === date.toDateString()
      );
      
      chartData.push({
        date: date.toISOString().split('T')[0],
        homeViews: dayData?.homeViews || 0,
        postViews: dayData?.postViews || 0,
        totalViews: dayData?.totalViews || 0,
        uniqueVisitors: dayData?.uniqueVisitors || 0
      });
    }

    res.status(200).json({
      success: true,
      chartData
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cannot get analytics chart data",
      error: err,
      success: false,
    });
  }
};

// Get popular posts
exports.getPopularPosts = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  try {
    ForbiddenError.from(ability).throwUnlessCan("read", "dashboard");

    const days = parseInt(req.query.days) || 7;
    const limit = parseInt(req.query.limit) || 10;
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const popularPosts = await PageView.aggregate([
      { 
        $match: { 
          timestamp: { $gte: startDate },
          page: 'post',
          postId: { $ne: null }
        }
      },
      { $group: { _id: '$postId', views: { $sum: 1 } } },
      { $sort: { views: -1 } },
      { $limit: limit },
      {
        $lookup: {
          from: 'posts',
          localField: '_id',
          foreignField: '_id',
          as: 'post'
        }
      },
      { $unwind: '$post' },
      {
        $project: {
          views: 1,
          title: '$post.title',
          slug: '$post.slug',
          createdAt: '$post.createdAt',
          isActive: '$post.isActive'
        }
      }
    ]);

    res.status(200).json({
      success: true,
      popularPosts
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cannot get popular posts",
      error: err,
      success: false,
    });
  }
};

// Track custom event (for future use)
exports.trackEvent = async (req, res) => {
  try {
    const { eventType, eventData } = req.body;
    
    // For now, just log the event
    console.log('Custom event tracked:', { eventType, eventData, timestamp: new Date() });
    
    res.status(200).json({
      success: true,
      message: 'Event tracked successfully'
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cannot track event",
      error: err,
      success: false,
    });
  }
};
