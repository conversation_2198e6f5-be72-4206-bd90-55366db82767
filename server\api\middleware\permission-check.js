const verifyPermission = (requiredPermission) => {
  return function(req, res, next) {
    const user = req.user;
    
    console.log('[DEBUG] verifyPermission called for permission:', requiredPermission);
    console.log('[DEBUG] User:', user ? { 
      id: user._id, 
      email: user.email, 
      rule: user.rule, 
      permissions: user.permissions 
    } : 'No user');
    
    if (!user) {
      const err = new Error('Authentication required');
      err.status = 401;
      return next(err);
    }

    // Admin always has all permissions
    if (user.rule === 'admin') {
      console.log('[DEBUG] User is admin, allowing access');
      return next();
    }

    // Check if user has the specific permission
    if (user.permissions && user.permissions.includes(requiredPermission)) {
      console.log('[DEBUG] User has required permission, allowing access');
      return next();
    }

    // User doesn't have required permission
    console.log('[DEBUG] User does not have required permission, denying access');
    const err = new Error('You are not authorized to perform this operation!');
    err.status = 403;
    return next(err);
  };
};

const verifyAnyPermission = (requiredPermissions) => {
  return function(req, res, next) {
    const user = req.user;
    
    if (!user) {
      const err = new Error('Authentication required');
      err.status = 401;
      return next(err);
    }

    // Admin always has all permissions
    if (user.rule === 'admin') {
      return next();
    }

    // Check if user has any of the required permissions
    if (user.permissions && requiredPermissions.some(permission => user.permissions.includes(permission))) {
      return next();
    }

    // User doesn't have any required permission
    const err = new Error('You are not authorized to perform this operation!');
    err.status = 403;
    return next(err);
  };
};

const verifyAllPermissions = (requiredPermissions) => {
  return function(req, res, next) {
    const user = req.user;
    
    if (!user) {
      const err = new Error('Authentication required');
      err.status = 401;
      return next(err);
    }

    // Admin always has all permissions
    if (user.rule === 'admin') {
      return next();
    }

    // Check if user has all required permissions
    if (user.permissions && requiredPermissions.every(permission => user.permissions.includes(permission))) {
      return next();
    }

    // User doesn't have all required permissions
    const err = new Error('You are not authorized to perform this operation!');
    err.status = 403;
    return next(err);
  };
};

// For dashboard stats - requires any dashboard-related permission
const verifyDashboardAccess = verifyAnyPermission([
  'user_view',
  'file_view', 
  'system_settings_view',
  'analytics_view'
]);

// For user management
const verifyUserView = verifyPermission('user_view');
const verifyUserAdd = verifyPermission('user_add');
const verifyUserEdit = verifyPermission('user_edit');
const verifyUserDelete = verifyPermission('user_delete');

// For file management
const verifyFileView = verifyPermission('file_view');
const verifyFileUpload = verifyPermission('file_upload');
const verifyFileDelete = verifyPermission('file_delete');

// For system settings
const verifySystemSettingsView = verifyPermission('system_settings_view');
const verifySystemSettingsEdit = verifyPermission('system_settings_edit');

module.exports = {
  verifyPermission,
  verifyAnyPermission,
  verifyAllPermissions,
  verifyDashboardAccess,
  verifyUserView,
  verifyUserAdd,
  verifyUserEdit,
  verifyUserDelete,
  verifyFileView,
  verifyFileUpload,
  verifyFileDelete,
  verifySystemSettingsView,
  verifySystemSettingsEdit
};
