const mongoose = require("mongoose");

// Schema for tracking page views
const PageViewSchema = new mongoose.Schema({
  page: {
    type: String,
    required: true,
    enum: ['home', 'post', 'category', 'search', 'about', 'contact', 'other']
  },
  path: {
    type: String,
    required: true
  },
  postId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post',
    required: false // Only for post views
  },
  userAgent: {
    type: String,
    required: false
  },
  ip: {
    type: String,
    required: false
  },
  referer: {
    type: String,
    required: false
  },
  sessionId: {
    type: String,
    required: false
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Schema for daily analytics summary
const DailyAnalyticsSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true,
    unique: true
  },
  homeViews: {
    type: Number,
    default: 0
  },
  postViews: {
    type: Number,
    default: 0
  },
  totalViews: {
    type: Number,
    default: 0
  },
  uniqueVisitors: {
    type: Number,
    default: 0
  },
  topPosts: [{
    postId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Post'
    },
    views: {
      type: Number,
      default: 0
    }
  }]
}, {
  timestamps: true
});

// Indexes for better performance
PageViewSchema.index({ timestamp: -1 });
PageViewSchema.index({ page: 1, timestamp: -1 });
PageViewSchema.index({ postId: 1, timestamp: -1 });
PageViewSchema.index({ ip: 1, timestamp: -1 });
PageViewSchema.index({ sessionId: 1, timestamp: -1 });

DailyAnalyticsSchema.index({ date: -1 });

const PageView = mongoose.model("PageView", PageViewSchema);
const DailyAnalytics = mongoose.model("DailyAnalytics", DailyAnalyticsSchema);

module.exports = { PageView, DailyAnalytics };
