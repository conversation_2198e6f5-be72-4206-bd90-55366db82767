"use client";
import { useEffect, useState, use } from "react";
import EditForm from "@/app/(private)/dashboard/user/add/edit-form";
import { AdminEditResBodyType, PasswordResBodyType } from "@/schemaValidations/user.schema"; // Import the schema
import userApiRequest from "@/apiRequests/user";
import { toast } from "react-toastify";
import Link from "next/link";
import PermissionGuard from "@/components/PermissionGuard";


export default function EditUser({ params }: { params: Promise<{ id: any }> }) {
  const [user, setUser] = useState<AdminEditResBodyType | null>(null);
  const resolvedParams = use(params);
  const userId = resolvedParams.id;


  useEffect(() => {
    const controller = new AbortController();
    const { signal } = controller;
  
    const fetchUser = async () => {
      try {
        const sessionToken = localStorage.getItem("sessionToken") || "";
        console.log("Fetching user with ID:", userId);
        console.log("Session token exists:", !!sessionToken);

        const result = await userApiRequest.fetchUserById(userId, sessionToken, signal);

        if (!signal.aborted) {
          console.log("User fetch result:", result);
          if (result.payload.success) {
            setUser(result.payload.user);
          } else {
            console.error("Error fetching user:", result.payload);
            toast.error("Failed to fetch user data: " + (result.payload?.message || "Lỗi không xác định"));
          }
        }
      } catch (error) {
        if (!signal.aborted) {
          console.error("Unexpected error:", error);
          toast.error("An error occurred while fetching user data");
        }
      }
    };
  
    if (userId) {
      fetchUser();
    }
  
    return () => {
      controller.abort(); // Cleanup function to cancel the request
    };
  }, [userId]);

  const handleUpdate = async (data: AdminEditResBodyType) => {
    try {
      console.log("Submitting user update data:", data);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await userApiRequest.updateUser( data, sessionToken );

      console.log("Update result:", result);
      if (result.payload.success) {
        setUser(result.payload.user);
        toast.success("Cập nhật thành công!");
      } else {
        console.error("Error updating user:", result.payload);
        toast.error("Không thể cập nhật: " + (result.payload?.message || "Lỗi không xác định"));
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("Có lỗi xảy ra khi cập nhật. Vui lòng thử lại.");
    }
  };

  const onSubmitPass = async (data: PasswordResBodyType) => {
    try {
      console.log("Submitting password change data:", data);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await userApiRequest.updatePassUser( data, sessionToken );

      console.log("Password change result:", result);
      if (result.payload.success) {
        toast.success("Đổi mật khẩu thành công!");
      } else {
        console.error("Error changing password:", result.payload);
        toast.error("Không thể đổi mật khẩu: " + (result.payload?.message || "Lỗi không xác định"));
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("Có lỗi xảy ra khi đổi mật khẩu. Vui lòng thử lại.");
    }
  };

  const handlePermissionsUpdate = async (userId: string, permissions: string[]) => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";

      // Create update data with permissions
      const updateData = {
        ...user,
        permissions: permissions
      };

      const result = await userApiRequest.updateUser(updateData, sessionToken);

      if (result.payload.success) {
        setUser(result.payload.user);
        toast.success("Cập nhật quyền thành công!");
      } else {
        console.error("Error updating permissions:", result.payload);
        toast.error("Không thể cập nhật quyền: " + (result.payload?.message || "Lỗi không xác định"));
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("Có lỗi xảy ra khi cập nhật quyền. Vui lòng thử lại.");
    }
  };

  return (
    <PermissionGuard requiredPermission="user_edit">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Chỉnh sửa tài khoản</h1>
          <Link
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            href={`/dashboard/user/log/${user?._id}`}
          >
            Xem User log
          </Link>
        </div>

        {user ? (
          <>
            {/* User Edit Form */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Thông tin tài khoản</h2>
              <EditForm onSubmit={handleUpdate} onSubmitPass={onSubmitPass} user={user} />
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-gray-500">Đang tải thông tin...</p>
            </div>
          </div>
        )}
      </div>
    </PermissionGuard>
  );
}
