const mongoose = require('mongoose');

const courtCaseSchema = new mongoose.Schema({
  // Thông tin thụ lý
  stt: {
    type: Number,
    unique: true,
    sparse: true // Allows null/undefined values while maintaining uniqueness
  },
  loaiAn: {
    type: String,
    enum: ['<PERSON><PERSON><PERSON> sự', '<PERSON><PERSON> sự', '<PERSON><PERSON><PERSON> chính', '<PERSON><PERSON> tế', 'Lao động', '<PERSON><PERSON><PERSON><PERSON>'],
    default: ''
  },
  soThuLy: {
    type: String,
    unique: true,
    sparse: true, // Allows null/undefined values while maintaining uniqueness
    default: ''
  },
  ngayThuLy: {
    type: Date,
    default: null
  },
  tand: {
    type: String,
    default: '' // Tòa án nhân dân
  },

  // Thông tin bản án/quyết định
  soBanAn: {
    type: String,
    default: ''
  },
  ngayBanHanh: {
    type: Date,
    default: null
  },
  biCaoNguoiKhieuKien: {
    type: String,
    default: '' // <PERSON><PERSON> cáo/Nguyên đơn/Người khiếu kiện
  },
  toiDanhNoiDung: {
    type: String,
    default: '' // Tội danh/Bồi dưỡng/Nội dung khiếu kiện
  },
  quanHePhatLuat: {
    type: String,
    default: '' // Tội danh/Quan hệ pháp luật
  },
  hinhThucXuLy: {
    type: String,
    enum: [
      'Tuyên vô tội',
      'Tuyên có tội - Tù',
      'Tuyên có tội - Phạt tiền',
      'Tuyên có tội - Cải tạo không giam giữ',
      'Tuyên có tội - Khác',
      'Bồi thường thiệt hại',
      'Từ chối khiếu kiện',
      'Chấp nhận khiếu kiện',
      'Khác',
      ''
    ],
    default: ''
  },
  thuTucApDung: {
    type: String,
    enum: ['Sơ thẩm', 'Phúc thẩm', 'Giám đốc thẩm', 'Tái thẩm', ''],
    default: ''
  },
  thamPhanPhuTrach: {
    type: String,
    default: ''
  },
  truongPhoPhongKTNV: {
    type: String,
    default: '' // Trưởng/Phó phòng KTNV/Thẩm tra viên
  },

  // Thông tin bổ sung
  ghiChu: {
    type: String,
    default: ''
  },
  ghiChuKetQua: {
    type: String,
    default: ''
  },
  trangThaiGiaiQuyet: {
    type: String,
    required: true,
    enum: ['Chưa giải quyết', 'Đang giải quyết', 'Đã giải quyết'],
    default: 'Chưa giải quyết'
  },

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  timestamps: true // Tự động tạo createdAt và updatedAt
});

// Indexes for better performance
courtCaseSchema.index({ soThuLy: 1 });
courtCaseSchema.index({ ngayThuLy: 1 });
courtCaseSchema.index({ loaiAn: 1 });
courtCaseSchema.index({ trangThaiGiaiQuyet: 1 });
courtCaseSchema.index({ createdAt: 1 });

// Virtual for formatted dates
courtCaseSchema.virtual('ngayThuLyFormatted').get(function() {
  return this.ngayThuLy ? this.ngayThuLy.toLocaleDateString('vi-VN') : '';
});

courtCaseSchema.virtual('ngayBanHanhFormatted').get(function() {
  return this.ngayBanHanh ? this.ngayBanHanh.toLocaleDateString('vi-VN') : '';
});

// Ensure virtual fields are serialized
courtCaseSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('CourtCase', courtCaseSchema);
